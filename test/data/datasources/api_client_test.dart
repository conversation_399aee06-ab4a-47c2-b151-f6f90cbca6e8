import 'package:flutter_test/flutter_test.dart';
import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import 'package:lucian_rides_app/data/datasources/api_client.dart';
import 'package:lucian_rides_app/data/datasources/storage_service.dart';
import 'package:lucian_rides_app/core/constants/app_constants.dart';

void main() {
  group('DioApiClient', () {
    late Dio dio;
    late StorageService storageService;
    late DioApiClient apiClient;

    setUp(() {
      dio = Dio();
      dio.options.baseUrl = AppConstants.baseUrl;
      storageService = SecureStorageService(const FlutterSecureStorage());
      apiClient = DioApiClient(dio, storageService);
    });

    test('should configure dio with correct base URL', () {
      expect(dio.options.baseUrl, equals(AppConstants.baseUrl));
    });

    test('should configure timeouts correctly', () {
      expect(dio.options.connectTimeout, equals(AppConstants.connectTimeout));
      expect(dio.options.receiveTimeout, equals(AppConstants.requestTimeout));
      expect(dio.options.sendTimeout, equals(AppConstants.requestTimeout));
    });

    test('should have interceptors configured', () {
      expect(dio.interceptors.length, greaterThan(0));
    });

    test('should update auth token without throwing', () {
      const testToken = 'test-token';

      // Should not throw
      expect(() => apiClient.updateAuthToken(testToken), returnsNormally);
    });

    test('should clear auth token without throwing', () {
      // Should not throw
      expect(() => apiClient.updateAuthToken(null), returnsNormally);
    });
  });
}
