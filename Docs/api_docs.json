{"openapi": "3.1.0", "info": {"title": "<PERSON>s", "description": "Backend API for St. Lucia Ride-Share MVP - Tourist-focused ride-sharing platform with fixed pricing", "version": "1.0.0"}, "paths": {"/api/v1/auth/register": {"post": {"tags": ["authentication", "authentication"], "summary": "Register", "description": "Register a new user account.\n\nArgs:\n    user_data: User registration data\n    \nReturns:\n    Token response with access token and user information\n    \nRaises:\n    HTTPException: If registration fails", "operationId": "register_api_v1_auth_register_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreate"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/login": {"post": {"tags": ["authentication", "authentication"], "summary": "<PERSON><PERSON>", "description": "Authenticate user and return access token.\n\nArgs:\n    login_data: User login credentials\n    \nReturns:\n    Token response with access token and user information\n    \nRaises:\n    HTTPException: If authentication fails", "operationId": "login_api_v1_auth_login_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserLogin"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/logout": {"post": {"tags": ["authentication", "authentication"], "summary": "Logout", "description": "Logout user (invalidate token on client side).\n\nArgs:\n    current_user: Current authenticated user\n    \nReturns:\n    Success response\n    \nNote:\n    JWT tokens are stateless, so logout is handled client-side\n    by removing the token from storage.", "operationId": "logout_api_v1_auth_logout_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__SuccessResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/auth/profile": {"get": {"tags": ["authentication", "authentication"], "summary": "Get Profile", "description": "Get current user profile.\n\nArgs:\n    current_user: Current authenticated user\n    \nReturns:\n    User profile information", "operationId": "get_profile_api_v1_auth_profile_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}}, "security": [{"HTTPBearer": []}]}, "put": {"tags": ["authentication", "authentication"], "summary": "Update Profile", "description": "Update current user profile.\n\nArgs:\n    update_data: Profile update data\n    current_user: Current authenticated user\n    \nReturns:\n    Updated user profile information\n    \nRaises:\n    HTTPException: If update fails", "operationId": "update_profile_api_v1_auth_profile_put", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/auth/profile/complete": {"get": {"tags": ["authentication", "authentication"], "summary": "Get Complete Profile", "description": "Get complete user profile with type-specific information.\n\nArgs:\n    current_user: Current authenticated user\n    \nReturns:\n    Complete user profile with rider/driver/admin information\n    \nRaises:\n    HTTPException: If profile retrieval fails", "operationId": "get_complete_profile_api_v1_auth_profile_complete_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserWithProfile"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/auth/profile/image": {"post": {"tags": ["authentication", "authentication"], "summary": "Upload Profile Image", "description": "Upload profile image for current user.\n\nArgs:\n    file: Image file to upload\n    current_user: Current authenticated user\n    \nReturns:\n    Profile image upload response with URL\n    \nRaises:\n    HTTPException: If upload fails", "operationId": "upload_profile_image_api_v1_auth_profile_image_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_upload_profile_image_api_v1_auth_profile_image_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProfileImageUpload"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}, "delete": {"tags": ["authentication", "authentication"], "summary": "Delete Profile Image", "description": "Delete current user's profile image.\n\nArgs:\n    current_user: Current authenticated user\n    \nReturns:\n    Success response\n    \nRaises:\n    HTTPException: If deletion fails", "operationId": "delete_profile_image_api_v1_auth_profile_image_delete", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__SuccessResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/auth/profile/image/info": {"get": {"tags": ["authentication", "authentication"], "summary": "Get Profile Image Info", "description": "Get profile image information and storage configuration.\n\nArgs:\n    current_user: Current authenticated user\n    \nReturns:\n    Profile image information and storage configuration", "operationId": "get_profile_image_info_api_v1_auth_profile_image_info_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Get Profile Image Info Api V1 Auth Profile Image Info Get"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/auth/verify-token": {"get": {"tags": ["authentication", "authentication"], "summary": "Verify <PERSON>", "description": "Verify JWT token and return user information.\n\nArgs:\n    current_user: Current authenticated user\n    \nReturns:\n    User information if token is valid", "operationId": "verify_token_api_v1_auth_verify_token_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/auth/me": {"get": {"tags": ["authentication", "authentication"], "summary": "Get Current User Info", "description": "Get current user information (alias for get_profile).\n\nArgs:\n    current_user: Current authenticated user\n    \nReturns:\n    Current user information", "operationId": "get_current_user_info_api_v1_auth_me_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/auth/deactivate": {"post": {"tags": ["authentication", "authentication"], "summary": "Deactivate Account", "description": "Deactivate current user account.\n\nArgs:\n    current_user: Current authenticated user\n    \nReturns:\n    Success response\n    \nRaises:\n    HTTPException: If deactivation fails", "operationId": "deactivate_account_api_v1_auth_deactivate_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__SuccessResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/auth/user-type": {"get": {"tags": ["authentication", "authentication"], "summary": "Get User Type", "description": "Get current user type.\n\nArgs:\n    current_user: Current authenticated user\n    \nReturns:\n    User type information", "operationId": "get_user_type_api_v1_auth_user_type_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Get User Type Api V1 Auth User Type Get"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/auth/permissions": {"get": {"tags": ["authentication", "authentication"], "summary": "Get User Permissions", "description": "Get current user permissions based on user type.\n\nArgs:\n    current_user: Current authenticated user\n    \nReturns:\n    User permissions information", "operationId": "get_user_permissions_api_v1_auth_permissions_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Get User Permissions Api V1 Auth Permissions Get"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/auth/profile/completeness": {"get": {"tags": ["authentication", "authentication"], "summary": "Get Profile Completeness", "description": "Get current user's profile completeness status.\n\nArgs:\n    current_user: Current authenticated user\n    \nReturns:\n    Profile completeness information\n    \nRaises:\n    HTTPException: If validation fails", "operationId": "get_profile_completeness_api_v1_auth_profile_completeness_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Get Profile Completeness Api V1 Auth Profile Completeness Get"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/auth/profile/validation": {"get": {"tags": ["authentication", "authentication"], "summary": "Get Profile Validation", "description": "Get detailed profile validation information.\n\nArgs:\n    current_user: Current authenticated user\n    \nReturns:\n    Detailed profile validation information\n    \nRaises:\n    HTTPException: If validation fails", "operationId": "get_profile_validation_api_v1_auth_profile_validation_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Get Profile Validation Api V1 Auth Profile Validation Get"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/auth/debug/token-info": {"get": {"tags": ["authentication", "authentication"], "summary": "Get Token Info", "description": "Get JWT token information for debugging.\n\nArgs:\n    credentials: HTTP authorization credentials\n    \nReturns:\n    Token information\n    \nNote:\n    This endpoint should only be available in development mode.", "operationId": "get_token_info_api_v1_auth_debug_token_info_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Get Token Info Api V1 Auth Debug Token Info Get"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/auth/health": {"get": {"tags": ["authentication", "authentication"], "summary": "Health Check", "description": "Health check endpoint for authentication service.\n\nReturns:\n    Health status information", "operationId": "health_check_api_v1_auth_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Health Check Api V1 Auth Health Get"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}}}}, "/api/v1/riders/profile": {"get": {"tags": ["riders", "riders"], "summary": "Get Rider Profile", "description": "Get current rider's profile.\n\nArgs:\n    current_rider: Current authenticated rider\n    \nReturns:\n    Rider profile information\n    \nRaises:\n    HTTPException: If profile not found or access denied", "operationId": "get_rider_profile_api_v1_riders_profile_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RiderProfileResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}}, "security": [{"HTTPBearer": []}]}, "put": {"tags": ["riders", "riders"], "summary": "Update Rider Profile", "description": "Update rider profile.\n\nArgs:\n    profile_data: Rider profile update data\n    current_rider: Current authenticated rider\n    \nReturns:\n    Updated rider profile\n    \nRaises:\n    HTTPException: If profile update fails", "operationId": "update_rider_profile_api_v1_riders_profile_put", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RiderProfileUpdate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RiderProfileResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}, "post": {"tags": ["riders", "riders"], "summary": "Create Rider Profile", "description": "Create rider profile.\n\nArgs:\n    profile_data: Rider profile creation data\n    current_rider: Current authenticated rider\n    \nReturns:\n    Created rider profile\n    \nRaises:\n    HTTPException: If profile creation fails", "operationId": "create_rider_profile_api_v1_riders_profile_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RiderProfileCreate"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RiderProfileResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/riders/emergency-contact": {"get": {"tags": ["riders", "riders"], "summary": "Get Emergency Contact", "description": "Get rider's emergency contact information.\n\nArgs:\n    current_rider: Current authenticated rider\n    \nReturns:\n    Emergency contact information\n    \nRaises:\n    HTTPException: If access denied or not found", "operationId": "get_emergency_contact_api_v1_riders_emergency_contact_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Get Emergency Contact Api V1 Riders Emergency Contact Get"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}}, "security": [{"HTTPBearer": []}]}, "post": {"tags": ["riders", "riders"], "summary": "Add Emergency Contact", "description": "Add or update emergency contact.\n\nArgs:\n    emergency_contact_data: Emergency contact data\n    current_rider: Current authenticated rider\n    \nReturns:\n    Success response\n    \nRaises:\n    HTTPException: If operation fails", "operationId": "add_emergency_contact_api_v1_riders_emergency_contact_post", "requestBody": {"content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Emergency Contact Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__SuccessResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}, "delete": {"tags": ["riders", "riders"], "summary": "Remove Emergency Contact", "description": "Remove emergency contact.\n\nArgs:\n    current_rider: Current authenticated rider\n    \nReturns:\n    Success response\n    \nRaises:\n    HTTPException: If operation fails", "operationId": "remove_emergency_contact_api_v1_riders_emergency_contact_delete", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__SuccessResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/riders/ride-preferences": {"get": {"tags": ["riders", "riders"], "summary": "Get Ride Preferences", "description": "Get rider's ride preferences.\n\nArgs:\n    current_rider: Current authenticated rider\n    \nReturns:\n    Ride preferences\n    \nRaises:\n    HTTPException: If access denied or not found", "operationId": "get_ride_preferences_api_v1_riders_ride_preferences_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Get Ride Preferences Api V1 Riders Ride Preferences Get"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}}, "security": [{"HTTPBearer": []}]}, "put": {"tags": ["riders", "riders"], "summary": "Update Ride Preferences", "description": "Update ride preferences.\n\nArgs:\n    preferences_data: Ride preferences data\n    current_rider: Current authenticated rider\n    \nReturns:\n    Success response\n    \nRaises:\n    HTTPException: If operation fails", "operationId": "update_ride_preferences_api_v1_riders_ride_preferences_put", "requestBody": {"content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Preferences Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__SuccessResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/riders/stats": {"get": {"tags": ["riders", "riders"], "summary": "Get Rider Stats", "description": "Get rider statistics.\n\nArgs:\n    current_rider: Current authenticated rider\n    \nReturns:\n    Rider statistics\n    \nRaises:\n    HTTPException: If access denied or not found", "operationId": "get_rider_stats_api_v1_riders_stats_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Get Rider Stats Api V1 Riders Stats Get"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/riders/health": {"get": {"tags": ["riders", "riders"], "summary": "Health Check", "description": "Health check endpoint for rider service.\n\nReturns:\n    Health status", "operationId": "health_check_api_v1_riders_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Health Check Api V1 Riders Health Get"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}}}}, "/api/v1/drivers/profile": {"get": {"tags": ["drivers", "drivers"], "summary": "Get Driver Profile", "description": "Get current driver's profile.\n\nArgs:\n    current_driver: Current authenticated driver\n    \nReturns:\n    Driver profile information\n    \nRaises:\n    HTTPException: If profile not found or access denied", "operationId": "get_driver_profile_api_v1_drivers_profile_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverProfileResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}}, "security": [{"HTTPBearer": []}]}, "put": {"tags": ["drivers", "drivers"], "summary": "Update Driver Profile", "description": "Update driver profile.\n\nArgs:\n    profile_data: Driver profile update data\n    current_driver: Current authenticated driver\n    \nReturns:\n    Updated driver profile\n    \nRaises:\n    HTTPException: If profile update fails", "operationId": "update_driver_profile_api_v1_drivers_profile_put", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverProfileUpdate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverProfileResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}, "post": {"tags": ["drivers", "drivers"], "summary": "Create Driver Profile", "description": "Create driver profile.\n\nArgs:\n    profile_data: Driver profile creation data\n    current_driver: Current authenticated driver\n    \nReturns:\n    Created driver profile\n    \nRaises:\n    HTTPException: If profile creation fails", "operationId": "create_driver_profile_api_v1_drivers_profile_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverProfileCreate"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverProfileResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/drivers/vehicle": {"get": {"tags": ["drivers", "drivers"], "summary": "Get Vehicle Info", "description": "Get driver's vehicle information.\n\nArgs:\n    current_driver: Current authenticated driver\n    \nReturns:\n    Vehicle information\n    \nRaises:\n    HTTPException: If access denied or not found", "operationId": "get_vehicle_info_api_v1_drivers_vehicle_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Get Vehicle Info Api V1 Drivers Vehicle Get"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}}, "security": [{"HTTPBearer": []}]}, "post": {"tags": ["drivers", "drivers"], "summary": "Add Vehicle Info", "description": "Add or update vehicle information.\n\nArgs:\n    vehicle_data: Vehicle information data\n    current_driver: Current authenticated driver\n    \nReturns:\n    Success response\n    \nRaises:\n    HTTPException: If operation fails", "operationId": "add_vehicle_info_api_v1_drivers_vehicle_post", "requestBody": {"content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Vehicle Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__SuccessResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/drivers/availability": {"put": {"tags": ["drivers", "drivers"], "summary": "Update Availability", "description": "Update driver availability status.\n\nArgs:\n    availability_data: Availability status data\n    current_driver: Current authenticated driver\n    \nReturns:\n    Success response\n    \nRaises:\n    HTTPException: If operation fails", "operationId": "update_availability_api_v1_drivers_availability_put", "requestBody": {"content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Availability Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__SuccessResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/drivers/location": {"get": {"tags": ["drivers", "drivers"], "summary": "Get Current Location", "description": "Get driver's current location.\n\nArgs:\n    current_driver: Current authenticated driver\n    \nReturns:\n    Current location information\n    \nRaises:\n    HTTPException: If access denied or not found", "operationId": "get_current_location_api_v1_drivers_location_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Get Current Location Api V1 Drivers Location Get"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}}, "security": [{"HTTPBearer": []}]}, "post": {"tags": ["drivers", "drivers"], "summary": "Update Location", "description": "Update driver's current location.\n\nArgs:\n    location_data: Location data\n    current_driver: Current authenticated driver\n    \nReturns:\n    Success response\n    \nRaises:\n    HTTPException: If operation fails", "operationId": "update_location_api_v1_drivers_location_post", "requestBody": {"content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Location Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__SuccessResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/drivers/verification-status": {"get": {"tags": ["drivers", "drivers"], "summary": "Get Verification Status", "description": "Get driver verification status.\n\nArgs:\n    current_driver: Current authenticated driver\n    \nReturns:\n    Verification status information\n    \nRaises:\n    HTTPException: If access denied or not found", "operationId": "get_verification_status_api_v1_drivers_verification_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Get Verification Status Api V1 Drivers Verification Status Get"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/drivers/earnings": {"get": {"tags": ["payments"], "summary": "Get driver earnings", "description": "Get comprehensive earnings summary for a driver", "operationId": "get_driver_earnings_api_v1_drivers_earnings_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EarningsResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/drivers/stats": {"get": {"tags": ["drivers", "drivers"], "summary": "Get Driver Stats", "description": "Get driver statistics.\n\nArgs:\n    current_driver: Current authenticated driver\n    \nReturns:\n    Driver statistics\n    \nRaises:\n    HTTPException: If access denied or not found", "operationId": "get_driver_stats_api_v1_drivers_stats_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Get Driver Stats Api V1 Drivers Stats Get"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/drivers/documents/upload": {"post": {"tags": ["drivers", "drivers"], "summary": "Upload Verification Document", "description": "Upload a verification document.\n\nArgs:\n    file: Document file to upload\n    document_type: Type of document (license, insurance, registration, etc.)\n    current_driver: Current authenticated driver\n    \nReturns:\n    Success response with document URL\n    \nRaises:\n    HTTPException: If upload fails", "operationId": "upload_verification_document_api_v1_drivers_documents_upload_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_upload_verification_document_api_v1_drivers_documents_upload_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__SuccessResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/drivers/documents": {"get": {"tags": ["drivers", "drivers"], "summary": "Get Verification Documents", "description": "Get all verification documents for the driver.\n\nArgs:\n    current_driver: Current authenticated driver\n    \nReturns:\n    List of verification documents\n    \nRaises:\n    HTTPException: If access denied or not found", "operationId": "get_verification_documents_api_v1_drivers_documents_get", "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Get Verification Documents Api V1 Drivers Documents Get"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}}}, "delete": {"tags": ["drivers", "drivers"], "summary": "Delete Verification Document", "description": "Delete a verification document.\n\nArgs:\n    document_url: Document URL to delete\n    current_driver: Current authenticated driver\n    \nReturns:\n    Success response\n    \nRaises:\n    HTTPException: If deletion fails", "operationId": "delete_verification_document_api_v1_drivers_documents_delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "document_url", "in": "query", "required": true, "schema": {"type": "string", "title": "Document Url"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__SuccessResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/drivers/verification/request": {"get": {"tags": ["drivers", "drivers"], "summary": "Get Verification Request Status", "description": "Get the current verification request status for the driver.\n\nArgs:\n    current_driver: Current authenticated driver\n    \nReturns:\n    Verification request status\n    \nRaises:\n    HTTPException: If access denied", "operationId": "get_verification_request_status_api_v1_drivers_verification_request_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Get Verification Request Status Api V1 Drivers Verification Request Get"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}}, "security": [{"HTTPBearer": []}]}, "post": {"tags": ["drivers", "drivers"], "summary": "Create Verification Request", "description": "Create a verification request for the driver.\n\nArgs:\n    request_data: Additional request data\n    current_driver: Current authenticated driver\n    \nReturns:\n    Success response\n    \nRaises:\n    HTTPException: If request creation fails", "operationId": "create_verification_request_api_v1_drivers_verification_request_post", "requestBody": {"content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Request Data", "default": {}}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__SuccessResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/drivers/health": {"get": {"tags": ["drivers", "drivers"], "summary": "Health Check", "description": "Health check endpoint for driver service.\n\nReturns:\n    Health status", "operationId": "health_check_api_v1_drivers_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Health Check Api V1 Drivers Health Get"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__user__ErrorResponse"}}}}}}}}}