{"designSystem": {"name": "Mobile Ride-Sharing App Design System", "version": "1.0.0", "description": "A comprehensive design system for mobile ride-sharing applications with clean, modern interfaces", "colorPalette": {"primary": {"teal": "#00B5A5", "darkTeal": "#008B7A", "lightTeal": "#4DD0C7"}, "neutral": {"white": "#FFFFFF", "lightGray": "#F5F5F5", "mediumGray": "#E0E0E0", "darkGray": "#666666", "charcoal": "#2C2C2C", "black": "#000000"}, "accent": {"blue": "#007AFF", "yellow": "#FFD700", "green": "#34C759", "orange": "#FF9500"}, "status": {"success": "#34C759", "warning": "#FF9500", "error": "#FF3B30", "info": "#007AFF"}}, "typography": {"fontFamily": {"primary": "Roboto", "secondary": "Inter", "fallback": "<PERSON><PERSON>, Inter, 'Open Sans', sans-serif"}, "googleFonts": {"imports": ["https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap", "https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap", "https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700&display=swap"], "preconnect": ["https://fonts.googleapis.com", "https://fonts.gstatic.com"]}, "fontWeights": {"light": 300, "regular": 400, "medium": 500, "semibold": 600, "bold": 700}, "fontSizes": {"xs": "12px", "sm": "14px", "base": "16px", "lg": "18px", "xl": "20px", "2xl": "24px", "3xl": "32px"}, "lineHeights": {"tight": 1.2, "normal": 1.4, "relaxed": 1.6}}, "spacing": {"xs": "4px", "sm": "8px", "md": "16px", "lg": "24px", "xl": "32px", "2xl": "48px", "3xl": "64px"}, "borderRadius": {"none": "0px", "sm": "4px", "md": "8px", "lg": "12px", "xl": "16px", "2xl": "24px", "full": "50%"}, "shadows": {"sm": "0 1px 2px 0 rgba(0, 0, 0, 0.05)", "md": "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)", "lg": "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)", "xl": "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"}, "layout": {"maxWidth": "375px", "statusBarHeight": "44px", "tabBarHeight": "83px", "headerHeight": "56px", "containerPadding": "16px", "sectionSpacing": "24px"}, "components": {"statusBar": {"height": "44px", "backgroundColor": "transparent", "textColor": "#000000", "elements": ["time", "signal", "wifi", "battery"], "fontSize": "14px", "fontWeight": "semibold"}, "navigationHeader": {"height": "56px", "backgroundColor": "#FFFFFF", "titleColor": "#000000", "titleSize": "18px", "titleWeight": "semibold", "backButton": {"size": "24px", "color": "#007AFF", "padding": "16px"}, "shadow": "sm"}, "tabBar": {"height": "83px", "backgroundColor": "#FFFFFF", "activeColor": "#007AFF", "inactiveColor": "#666666", "iconSize": "24px", "labelSize": "10px", "labelWeight": "medium", "shadow": "md", "borderRadius": "none"}, "cards": {"backgroundColor": "#FFFFFF", "borderRadius": "12px", "shadow": "md", "padding": "16px", "marginBottom": "16px", "border": "none"}, "listItems": {"height": "72px", "backgroundColor": "#FFFFFF", "borderBottom": "1px solid #E0E0E0", "padding": "16px", "iconSize": "24px", "titleSize": "16px", "titleWeight": "medium", "titleColor": "#000000", "subtitleSize": "14px", "subtitleWeight": "regular", "subtitleColor": "#666666"}, "buttons": {"primary": {"backgroundColor": "#00B5A5", "textColor": "#FFFFFF", "borderRadius": "12px", "height": "48px", "fontSize": "16px", "fontWeight": "semibold", "shadow": "sm"}, "secondary": {"backgroundColor": "#F5F5F5", "textColor": "#000000", "borderRadius": "12px", "height": "48px", "fontSize": "16px", "fontWeight": "medium", "border": "1px solid #E0E0E0"}, "ghost": {"backgroundColor": "transparent", "textColor": "#007AFF", "borderRadius": "12px", "height": "48px", "fontSize": "16px", "fontWeight": "medium"}}, "inputFields": {"backgroundColor": "#F5F5F5", "borderRadius": "12px", "height": "48px", "padding": "16px", "fontSize": "16px", "fontWeight": "regular", "textColor": "#000000", "placeholderColor": "#666666", "border": "1px solid #E0E0E0", "focusBorderColor": "#00B5A5"}, "mapContainer": {"backgroundColor": "#2C2C2C", "borderRadius": "12px", "aspectRatio": "16:9", "overlay": {"backgroundColor": "rgba(0, 0, 0, 0.6)", "textColor": "#FFFFFF"}}, "locationPins": {"pickup": {"color": "#00B5A5", "size": "24px", "backgroundColor": "#FFFFFF", "borderRadius": "50%", "shadow": "md"}, "destination": {"color": "#007AFF", "size": "24px", "backgroundColor": "#FFFFFF", "borderRadius": "50%", "shadow": "md"}}, "ratingStars": {"size": "16px", "activeColor": "#FFD700", "inactiveColor": "#E0E0E0", "spacing": "2px"}, "badges": {"backgroundColor": "#FF3B30", "textColor": "#FFFFFF", "borderRadius": "50%", "minWidth": "20px", "height": "20px", "fontSize": "12px", "fontWeight": "bold"}, "modals": {"backgroundColor": "#FFFFFF", "borderRadius": "16px", "shadow": "xl", "padding": "24px", "maxWidth": "90%", "overlay": {"backgroundColor": "rgba(0, 0, 0, 0.5)"}}, "bottomSheets": {"backgroundColor": "#FFFFFF", "borderRadius": "16px 16px 0 0", "shadow": "xl", "padding": "24px", "handle": {"backgroundColor": "#E0E0E0", "width": "36px", "height": "4px", "borderRadius": "2px", "marginBottom": "16px"}}}, "patterns": {"orderHistory": {"structure": "List of cards with pickup/destination, payment, distance/time info", "grouping": "Active orders at top, past orders below", "itemHeight": "Variable based on content", "spacing": "8px between items"}, "addressSelection": {"structure": "Search field + recent addresses list", "searchPlaceholder": "Enter address or location name", "recentItems": "Location icon + address + city", "maxRecentItems": 5}, "rideBooking": {"structure": "Map + route + bottom sheet with vehicle options", "mapInteraction": "Tap to select pickup/destination", "vehicleSelection": "Horizontal scrollable cards", "pricingDisplay": "Prominent price with estimated time"}, "navigation": {"structure": "Bottom tab bar with 4-5 main sections", "tabIcons": "Simple line icons", "activeState": "Blue color + filled icon", "badges": "Red notification badges on relevant tabs"}}, "animations": {"transitions": {"duration": "300ms", "easing": "ease-in-out"}, "loading": {"type": "Spinner or skeleton screens", "color": "#00B5A5"}, "interactions": {"buttonPress": "Scale down to 0.95", "cardTap": "Slight scale and shadow increase", "listItemTap": "Background color change"}}, "accessibility": {"minTouchTarget": "44px", "colorContrast": "WCAG AA compliant", "textScaling": "Support for dynamic type", "voiceOver": "Proper labels and hints"}, "responsiveness": {"breakpoints": {"mobile": "320px - 414px", "tablet": "415px - 768px"}, "scalingFactors": {"text": "1.0x - 1.2x", "spacing": "1.0x - 1.5x", "components": "Maintain aspect ratios"}}}, "implementationGuidelines": {"codeStructure": {"componentHierarchy": "Atomic design principles", "stateManagement": "Context API or Redux for complex state", "styling": "CSS-in-JS or styled-components", "responsiveness": "Flexbox and CSS Grid"}, "bestPractices": {"consistency": "Use design tokens for all values", "performance": "Lazy load components and images", "accessibility": "Test with screen readers", "testing": "Unit tests for components, e2e for user flows"}, "commonPatterns": {"dataFetching": "Loading states with skeleton screens", "errorHandling": "Graceful error messages with retry options", "formValidation": "Real-time validation with clear error messages", "navigation": "Consistent back button behavior and deep linking"}}}