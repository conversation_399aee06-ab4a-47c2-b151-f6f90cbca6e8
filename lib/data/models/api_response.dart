import 'package:freezed_annotation/freezed_annotation.dart';

part 'api_response.freezed.dart';
part 'api_response.g.dart';

/// Generic API response wrapper for handling success and error responses
@Freezed(genericArgumentFactories: true)
class ApiResponse<T> with _$ApiResponse<T> {
  /// Successful API response with data and optional message
  const factory ApiResponse.success({required T data, String? message}) =
      ApiSuccess<T>;

  /// Error API response with message, error code, and status code
  const factory ApiResponse.error({
    required String message,
    String? errorCode,
    int? statusCode,
  }) = ApiError<T>;

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object?) fromJsonT,
  ) => _$ApiResponseFromJson(json, fromJsonT);
}
