// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_registration.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserRegistrationImpl _$$UserRegistrationImplFromJson(
        Map<String, dynamic> json) =>
    _$UserRegistrationImpl(
      email: json['email'] as String,
      password: json['password'] as String,
      confirmPassword: json['confirmPassword'] as String,
      name: json['name'] as String,
      userType: $enumDecode(_$UserTypeEnumMap, json['userType']),
      phone: json['phone'] as String?,
    );

Map<String, dynamic> _$$UserRegistrationImplToJson(
        _$UserRegistrationImpl instance) =>
    <String, dynamic>{
      'email': instance.email,
      'password': instance.password,
      'confirmPassword': instance.confirmPassword,
      'name': instance.name,
      'userType': _$UserTypeEnumMap[instance.userType]!,
      if (instance.phone case final value?) 'phone': value,
    };

const _$UserTypeEnumMap = {
  UserType.rider: 'rider',
  UserType.driver: 'driver',
};
