import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/user.dart';

part 'auth_result.freezed.dart';
part 'auth_result.g.dart';

/// Authentication result model that represents the outcome of authentication operations
@freezed
class AuthResult with _$AuthResult {
  /// Successful authentication with token and user data
  const factory AuthResult.success({
    required String token,
    required User user,
  }) = AuthSuccess;

  /// Failed authentication with error message and optional error code
  const factory AuthResult.failure({
    required String message,
    String? errorCode,
  }) = AuthFailure;

  factory AuthResult.fromJson(Map<String, dynamic> json) =>
      _$AuthResultFromJson(json);
}
