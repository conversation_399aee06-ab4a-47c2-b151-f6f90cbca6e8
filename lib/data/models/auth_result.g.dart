// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_result.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AuthSuccessImpl _$$AuthSuccessImplFromJson(Map<String, dynamic> json) =>
    _$AuthSuccessImpl(
      token: json['token'] as String,
      user: User.fromJson(json['user'] as Map<String, dynamic>),
      $type: json['runtimeType'] as String?,
    );

Map<String, dynamic> _$$AuthSuccessImplToJson(_$AuthSuccessImpl instance) =>
    <String, dynamic>{
      'token': instance.token,
      'user': instance.user.toJson(),
      'runtimeType': instance.$type,
    };

_$AuthFailureImpl _$$AuthFailureImplFromJson(Map<String, dynamic> json) =>
    _$AuthFailureImpl(
      message: json['message'] as String,
      errorCode: json['errorCode'] as String?,
      $type: json['runtimeType'] as String?,
    );

Map<String, dynamic> _$$AuthFailureImplToJson(_$AuthFailureImpl instance) =>
    <String, dynamic>{
      'message': instance.message,
      if (instance.errorCode case final value?) 'errorCode': value,
      'runtimeType': instance.$type,
    };
