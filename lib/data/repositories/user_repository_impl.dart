import 'dart:io';
import 'package:dio/dio.dart';
import '../../domain/entities/user.dart';
import '../../domain/repositories/user_repository.dart';
import '../../core/constants/app_constants.dart';
import '../../core/errors/app_error.dart';
import '../datasources/api_client.dart';
import '../datasources/storage_service.dart';

/// Implementation of UserRepository with enhanced error handling and data transformation
class UserRepositoryImpl implements UserRepository {
  final ApiClient _apiClient;
  final StorageService _storageService;

  UserRepositoryImpl({
    required ApiClient apiClient,
    required StorageService storageService,
  }) : _apiClient = apiClient,
       _storageService = storageService;

  @override
  Future<User> getProfile() async {
    try {
      // First try to get user from local storage for better performance
      final cachedUser = await _storageService.getUserData();
      if (cachedUser != null) {
        // Optionally refresh from server in background
        _refreshProfileInBackground();
        return cachedUser;
      }

      // If not cached, fetch from server
      final response = await _apiClient.get(AppConstants.authProfile);
      final responseData = response.data as Map<String, dynamic>;
      final user = User.fromJson(responseData);

      // Cache the user data for future use
      await _storageService.storeUserData(user);

      return user;
    } catch (e) {
      if (e is AppError) rethrow;
      throw AppError.server(
        message: 'Failed to get user profile: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  @override
  Future<User> getProfileFromServer() async {
    try {
      // Always fetch fresh data from server, bypassing cache
      final response = await _apiClient.get(AppConstants.authProfile);
      final responseData = response.data as Map<String, dynamic>;
      final user = User.fromJson(responseData);

      // Update cache with fresh data
      await _storageService.storeUserData(user);

      return user;
    } catch (e) {
      if (e is AppError) rethrow;
      throw AppError.server(
        message: 'Failed to get user profile from server: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  @override
  Future<User> updateProfile(User user) async {
    try {
      // Validate user data before sending
      if (user.email.isEmpty || user.name.isEmpty) {
        throw const AppError.validation(
          message: 'Email and name are required',
          fieldErrors: {
            'email': 'Email cannot be empty',
            'name': 'Name cannot be empty',
          },
        );
      }

      // Make API call with proper endpoint
      final response = await _apiClient.put(
        AppConstants.authProfile,
        data: user.toJson(),
      );

      // Extract and validate response data
      final responseData = response.data as Map<String, dynamic>;
      final updatedUser = User.fromJson(responseData);

      // Update cached user data
      await _storageService.storeUserData(updatedUser);

      return updatedUser;
    } catch (e) {
      if (e is AppError) rethrow;
      throw AppError.server(
        message: 'Failed to update profile: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  @override
  Future<String> uploadProfileImage(String imagePath) async {
    try {
      // Validate file exists
      final file = File(imagePath);
      if (!await file.exists()) {
        throw const AppError.validation(
          message: 'Selected image file does not exist',
          fieldErrors: {'image': 'File not found'},
        );
      }

      // Validate file size (limit to 5MB)
      final fileSize = await file.length();
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (fileSize > maxSize) {
        throw const AppError.validation(
          message: 'Image file is too large. Maximum size is 5MB',
          fieldErrors: {'image': 'File size exceeds 5MB limit'},
        );
      }

      // Create multipart form data for file upload
      final fileName = file.path.split('/').last;
      final formData = FormData.fromMap({
        'image': await MultipartFile.fromFile(imagePath, filename: fileName),
      });

      // Upload image to server
      final response = await _apiClient.post(
        AppConstants.authProfileImage,
        data: formData,
      );

      // Extract image URL from response
      final responseData = response.data as Map<String, dynamic>;
      final imageUrl = responseData['image_url'] as String?;

      if (imageUrl == null || imageUrl.isEmpty) {
        throw const AppError.server(
          message: 'Invalid response: missing image URL',
          statusCode: 500,
        );
      }

      // Update cached user data with new profile image
      final currentUser = await _storageService.getUserData();
      if (currentUser != null) {
        final updatedUser = currentUser.copyWith(profileImage: imageUrl);
        await _storageService.storeUserData(updatedUser);
      }

      return imageUrl;
    } catch (e) {
      if (e is AppError) rethrow;
      throw AppError.server(
        message: 'Failed to upload profile image: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  @override
  Future<void> deleteProfileImage() async {
    try {
      // Delete image from server
      await _apiClient.delete(AppConstants.authProfileImage);

      // Update cached user data to remove profile image
      final currentUser = await _storageService.getUserData();
      if (currentUser != null) {
        final updatedUser = currentUser.copyWith(profileImage: null);
        await _storageService.storeUserData(updatedUser);
      }
    } catch (e) {
      if (e is AppError) rethrow;
      throw AppError.server(
        message: 'Failed to delete profile image: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  /// Refresh profile data in background without blocking UI
  void _refreshProfileInBackground() {
    Future.microtask(() async {
      try {
        final response = await _apiClient.get(AppConstants.authProfile);
        final responseData = response.data as Map<String, dynamic>;
        final user = User.fromJson(responseData);
        await _storageService.storeUserData(user);
      } catch (e) {
        // Silently fail background refresh
        // The cached data will continue to be used
      }
    });
  }
}
