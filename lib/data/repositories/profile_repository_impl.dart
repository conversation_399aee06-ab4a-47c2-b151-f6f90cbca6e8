import 'dart:io';
import 'package:dio/dio.dart';
import '../../domain/entities/rider_profile.dart';
import '../../domain/repositories/profile_repository.dart';
import '../../core/constants/app_constants.dart';
import '../../core/errors/app_error.dart';
import '../datasources/api_client.dart';
import '../datasources/storage_service.dart';

/// Implementation of ProfileRepository with API integration
class ProfileRepositoryImpl implements ProfileRepository {
  final ApiClient _apiClient;

  ProfileRepositoryImpl({
    required ApiClient apiClient,
    required StorageService
    storageService, // Keep parameter for DI compatibility
  }) : _apiClient = apiClient;

  // Rider profile operations
  @override
  Future<RiderProfile?> getRiderProfile() async {
    try {
      final response = await _apiClient.get(AppConstants.ridersProfile);
      final responseData = response.data as Map<String, dynamic>;
      return RiderProfile.fromJson(responseData);
    } catch (e) {
      if (e is AppError) {
        // If profile doesn't exist (404), return null
        if (e is ServerError && e.statusCode == 404) {
          return null;
        }
        rethrow;
      }
      throw AppError.server(
        message: 'Failed to get rider profile: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  @override
  Future<RiderProfile> createRiderProfile(RiderProfile profile) async {
    try {
      final response = await _apiClient.post(
        AppConstants.ridersProfile,
        data: profile.toJson(),
      );
      final responseData = response.data as Map<String, dynamic>;
      return RiderProfile.fromJson(responseData);
    } catch (e) {
      if (e is AppError) rethrow;
      throw AppError.server(
        message: 'Failed to create rider profile: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  @override
  Future<RiderProfile> updateRiderProfile(RiderProfile profile) async {
    try {
      final response = await _apiClient.put(
        AppConstants.ridersProfile,
        data: profile.toJson(),
      );
      final responseData = response.data as Map<String, dynamic>;
      return RiderProfile.fromJson(responseData);
    } catch (e) {
      if (e is AppError) rethrow;
      throw AppError.server(
        message: 'Failed to update rider profile: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  // Profile image operations
  @override
  Future<String> uploadProfileImage(String imagePath) async {
    try {
      // Validate file exists
      final file = File(imagePath);
      if (!await file.exists()) {
        throw const AppError.validation(
          message: 'Selected image file does not exist',
          fieldErrors: {'image': 'File not found'},
        );
      }

      // Validate file size (limit to 5MB)
      final fileSize = await file.length();
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (fileSize > maxSize) {
        throw const AppError.validation(
          message: 'Image file is too large. Maximum size is 5MB',
          fieldErrors: {'image': 'File size exceeds 5MB limit'},
        );
      }

      // Create multipart form data for file upload
      final fileName = file.path.split('/').last;
      final formData = FormData.fromMap({
        'image': await MultipartFile.fromFile(imagePath, filename: fileName),
      });

      // Upload image to server
      final response = await _apiClient.post(
        AppConstants.authProfileImage,
        data: formData,
      );

      // Extract image URL from response
      final responseData = response.data as Map<String, dynamic>;
      final imageUrl = responseData['image_url'] as String?;

      if (imageUrl == null || imageUrl.isEmpty) {
        throw const AppError.server(
          message: 'Invalid response: missing image URL',
          statusCode: 500,
        );
      }

      return imageUrl;
    } catch (e) {
      if (e is AppError) rethrow;
      throw AppError.server(
        message: 'Failed to upload profile image: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  @override
  Future<void> deleteProfileImage() async {
    try {
      await _apiClient.delete(AppConstants.authProfileImage);
    } catch (e) {
      if (e is AppError) rethrow;
      throw AppError.server(
        message: 'Failed to delete profile image: ${e.toString()}',
        statusCode: 500,
      );
    }
  }
}
