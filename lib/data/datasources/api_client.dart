import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../../core/constants/app_constants.dart';
import '../../core/errors/error_handler.dart';
import 'auth_interceptor.dart';
import 'storage_service.dart';

abstract class ApiClient {
  Future<Response<T>> get<T>(String path, {Map<String, dynamic>? queryParams});
  Future<Response<T>> post<T>(String path, {dynamic data});
  Future<Response<T>> put<T>(String path, {dynamic data});
  Future<Response<T>> delete<T>(String path);
  Future<Response<T>> patch<T>(String path, {dynamic data});
  void updateAuthToken(String? token);
}

class DioApiClient implements ApiClient {
  final Dio _dio;
  final AuthInterceptor _authInterceptor;

  DioApiClient(this._dio, StorageService storageService)
    : _authInterceptor = AuthInterceptor(storageService) {
    _setupInterceptors();
  }

  void _setupInterceptors() {
    // Clear existing interceptors
    _dio.interceptors.clear();

    // Add authentication interceptor
    _dio.interceptors.add(_authInterceptor);

    // Add logging interceptor in debug mode
    if (AppConstants.enableLogging && kDebugMode) {
      _dio.interceptors.add(
        LogInterceptor(
          requestHeader: true,
          requestBody: true,
          responseHeader: false,
          responseBody: true,
          error: true,
          logPrint: (object) {
            debugPrint('[API] $object');
          },
        ),
      );
    }

    // Configure timeouts
    _dio.options.connectTimeout = AppConstants.connectTimeout;
    _dio.options.receiveTimeout = AppConstants.requestTimeout;
    _dio.options.sendTimeout = AppConstants.requestTimeout;
  }

  @override
  void updateAuthToken(String? token) {
    _authInterceptor.updateToken(token);
  }

  @override
  Future<Response<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParams,
  }) async {
    try {
      return await _dio.get<T>(path, queryParameters: queryParams);
    } catch (error) {
      throw ErrorHandler.handleError(error);
    }
  }

  @override
  Future<Response<T>> post<T>(String path, {dynamic data}) async {
    try {
      return await _dio.post<T>(path, data: data);
    } catch (error) {
      throw ErrorHandler.handleError(error);
    }
  }

  @override
  Future<Response<T>> put<T>(String path, {dynamic data}) async {
    try {
      return await _dio.put<T>(path, data: data);
    } catch (error) {
      throw ErrorHandler.handleError(error);
    }
  }

  @override
  Future<Response<T>> patch<T>(String path, {dynamic data}) async {
    try {
      return await _dio.patch<T>(path, data: data);
    } catch (error) {
      throw ErrorHandler.handleError(error);
    }
  }

  @override
  Future<Response<T>> delete<T>(String path) async {
    try {
      return await _dio.delete<T>(path);
    } catch (error) {
      throw ErrorHandler.handleError(error);
    }
  }
}
