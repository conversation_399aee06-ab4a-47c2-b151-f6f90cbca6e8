import 'package:dio/dio.dart';
import '../../core/constants/app_constants.dart';
import 'api_client.dart';
import 'storage_service.dart';

class ApiClientFactory {
  static ApiClient create(StorageService storageService) {
    final dio = Dio();

    // Configure base options
    dio.options.baseUrl = AppConstants.baseUrl;
    dio.options.connectTimeout = AppConstants.connectTimeout;
    dio.options.receiveTimeout = AppConstants.requestTimeout;
    dio.options.sendTimeout = AppConstants.requestTimeout;

    // Set default headers
    dio.options.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    return DioApiClient(dio, storageService);
  }
}
