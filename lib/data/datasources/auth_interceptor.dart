import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../../core/constants/app_constants.dart';
import 'storage_service.dart';

class AuthInterceptor extends Interceptor {
  final StorageService _storageService;
  String? _cachedToken;

  AuthInterceptor(this._storageService);

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    try {
      // Get token from cache or storage
      _cachedToken ??= await _storageService.getToken();

      if (_cachedToken != null) {
        options.headers['Authorization'] = 'Bearer $_cachedToken';
      }

      // Add common headers
      options.headers['Content-Type'] = 'application/json';
      options.headers['Accept'] = 'application/json';

      // Add user agent for API tracking
      options.headers['User-Agent'] =
          '${AppConstants.appName}/${AppConstants.appVersion}';

      if (kDebugMode) {
        debugPrint('[AUTH] Adding token to request: ${options.path}');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('[AUTH] Error adding token: $e');
      }
    }

    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (kDebugMode) {
      debugPrint('[AUTH] Response received: ${response.statusCode}');
    }
    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (kDebugMode) {
      debugPrint('[AUTH] Error occurred: ${err.response?.statusCode}');
    }

    // Handle 401 Unauthorized errors
    if (err.response?.statusCode == 401) {
      await _handleUnauthorized();
    }

    handler.next(err);
  }

  Future<void> _handleUnauthorized() async {
    try {
      // Clear cached token and stored token
      _cachedToken = null;
      await _storageService.clearToken();

      if (kDebugMode) {
        debugPrint('[AUTH] Cleared tokens due to 401 error');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('[AUTH] Error clearing tokens: $e');
      }
    }
  }

  /// Update the cached token (called when user logs in)
  void updateToken(String? token) {
    _cachedToken = token;
    if (kDebugMode) {
      debugPrint('[AUTH] Token updated: ${token != null ? 'Set' : 'Cleared'}');
    }
  }

  /// Clear the cached token (called when user logs out)
  void clearToken() {
    _cachedToken = null;
    if (kDebugMode) {
      debugPrint('[AUTH] Token cleared from cache');
    }
  }
}
