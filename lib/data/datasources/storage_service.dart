import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../../core/constants/app_constants.dart';
import '../../domain/entities/user.dart';

/// Abstract interface for secure storage operations
abstract class StorageService {
  /// Store JWT authentication token securely
  Future<void> storeToken(String token);

  /// Retrieve stored JWT token
  Future<String?> getToken();

  /// Clear stored JWT token
  Future<void> clearToken();

  /// Store user data object securely
  Future<void> storeUserData(User user);

  /// Retrieve stored user data
  Future<User?> getUserData();

  /// Clear stored user data
  Future<void> clearUserData();

  /// Store app language preference
  Future<void> storeLanguage(String language);

  /// Retrieve stored language preference
  Future<String?> getLanguage();

  /// Store theme mode preference
  Future<void> storeThemeMode(String themeMode);

  /// Retrieve stored theme mode
  Future<String?> getThemeMode();

  /// Store onboarding completion status
  Future<void> storeOnboardingCompleted(bool completed);

  /// Check if onboarding is completed
  Future<bool> isOnboardingCompleted();

  /// Clear all stored data
  Future<void> clearAll();

  /// Check if token exists
  Future<bool> hasToken();

  /// Check if user data exists
  Future<bool> hasUserData();
}

/// Secure storage service implementation using Flutter Secure Storage
class SecureStorageService implements StorageService {
  final FlutterSecureStorage _storage;

  SecureStorageService(this._storage);

  @override
  Future<void> storeToken(String token) async {
    await _storage.write(key: AppConstants.tokenKey, value: token);
  }

  @override
  Future<String?> getToken() async {
    return await _storage.read(key: AppConstants.tokenKey);
  }

  @override
  Future<void> clearToken() async {
    await _storage.delete(key: AppConstants.tokenKey);
  }

  @override
  Future<void> storeUserData(User user) async {
    final userJson = jsonEncode(user.toJson());
    await _storage.write(key: AppConstants.userDataKey, value: userJson);
  }

  @override
  Future<User?> getUserData() async {
    final userJson = await _storage.read(key: AppConstants.userDataKey);
    if (userJson == null) return null;

    try {
      final userMap = jsonDecode(userJson) as Map<String, dynamic>;
      return User.fromJson(userMap);
    } catch (e) {
      // If parsing fails, clear corrupted data
      await clearUserData();
      return null;
    }
  }

  @override
  Future<void> clearUserData() async {
    await _storage.delete(key: AppConstants.userDataKey);
  }

  @override
  Future<void> storeLanguage(String language) async {
    await _storage.write(key: AppConstants.languageKey, value: language);
  }

  @override
  Future<String?> getLanguage() async {
    return await _storage.read(key: AppConstants.languageKey);
  }

  @override
  Future<void> storeThemeMode(String themeMode) async {
    await _storage.write(key: AppConstants.themeKey, value: themeMode);
  }

  @override
  Future<String?> getThemeMode() async {
    return await _storage.read(key: AppConstants.themeKey);
  }

  @override
  Future<void> storeOnboardingCompleted(bool completed) async {
    await _storage.write(
      key: AppConstants.onboardingKey,
      value: completed.toString(),
    );
  }

  @override
  Future<bool> isOnboardingCompleted() async {
    final value = await _storage.read(key: AppConstants.onboardingKey);
    return value == 'true';
  }

  @override
  Future<void> clearAll() async {
    await _storage.deleteAll();
  }

  @override
  Future<bool> hasToken() async {
    final token = await getToken();
    return token != null && token.isNotEmpty;
  }

  @override
  Future<bool> hasUserData() async {
    final userData = await getUserData();
    return userData != null;
  }
}
