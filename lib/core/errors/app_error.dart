import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_error.freezed.dart';

@freezed
class AppError with _$AppError {
  const factory AppError.network({required String message, String? details}) =
      NetworkError;

  const factory AppError.authentication({
    required String message,
    String? errorCode,
  }) = AuthenticationError;

  const factory AppError.validation({
    required String message,
    required Map<String, String> fieldErrors,
  }) = ValidationError;

  const factory AppError.server({
    required String message,
    required int statusCode,
  }) = ServerError;

  const factory AppError.unknown({required String message, Object? exception}) =
      UnknownError;
}
