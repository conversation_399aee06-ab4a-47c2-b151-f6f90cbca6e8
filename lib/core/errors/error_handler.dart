import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'app_error.dart';

/// Utility class for handling and converting various error types to AppError
class ErrorHandler {
  /// Converts a generic exception to an AppError
  static AppError handleError(dynamic error) {
    if (error is AppError) {
      return error;
    }

    if (error is DioException) {
      return _handleDioError(error);
    }

    if (error is SocketException) {
      return AppError.network(
        message: 'No internet connection. Please check your network settings.',
        details: error.message,
      );
    }

    if (error is FormatException) {
      return AppError.unknown(
        message: 'Invalid data format received from server.',
        exception: error,
      );
    }

    // Log unknown errors in debug mode
    if (kDebugMode) {
      debugPrint('Unknown error: $error');
    }

    return AppError.unknown(
      message: 'An unexpected error occurred. Please try again.',
      exception: error,
    );
  }

  /// Handles Dio-specific errors and converts them to AppError
  static AppError _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return AppError.network(
          message: 'Connection timeout. Please check your internet connection.',
          details: error.message,
        );

      case DioExceptionType.badResponse:
        return _handleHttpError(error);

      case DioExceptionType.cancel:
        return AppError.network(
          message: 'Request was cancelled.',
          details: error.message,
        );

      case DioExceptionType.connectionError:
        return AppError.network(
          message:
              'Unable to connect to server. Please check your internet connection.',
          details: error.message,
        );

      case DioExceptionType.badCertificate:
        return AppError.network(
          message: 'Security certificate error. Please try again later.',
          details: error.message,
        );

      case DioExceptionType.unknown:
        return AppError.unknown(
          message: 'Network error occurred. Please try again.',
          exception: error,
        );
    }
  }

  /// Handles HTTP response errors based on status codes
  static AppError _handleHttpError(DioException error) {
    final statusCode = error.response?.statusCode ?? 0;
    final responseData = error.response?.data;

    // Try to extract error message from response
    String message =
        _extractErrorMessage(responseData) ??
        _getDefaultErrorMessage(statusCode);

    switch (statusCode) {
      case 400:
        // Handle validation errors
        if (responseData is Map<String, dynamic> &&
            responseData.containsKey('field_errors')) {
          final fieldErrors = Map<String, String>.from(
            responseData['field_errors'] as Map<String, dynamic>,
          );
          return AppError.validation(
            message: message,
            fieldErrors: fieldErrors,
          );
        }
        return AppError.server(message: message, statusCode: statusCode);

      case 401:
        return AppError.authentication(
          message: message,
          errorCode: 'UNAUTHORIZED',
        );

      case 403:
        return AppError.authentication(
          message: message,
          errorCode: 'FORBIDDEN',
        );

      case 404:
        return AppError.server(
          message: 'The requested resource was not found.',
          statusCode: statusCode,
        );

      case 422:
        // Handle validation errors from FastAPI
        if (responseData is Map<String, dynamic> &&
            responseData.containsKey('detail')) {
          final details = responseData['detail'];
          if (details is List) {
            final fieldErrors = <String, String>{};
            for (final detail in details) {
              if (detail is Map<String, dynamic>) {
                final field =
                    (detail['loc'] as List?)?.last?.toString() ?? 'field';
                final msg = detail['msg']?.toString() ?? 'Invalid value';
                fieldErrors[field] = msg;
              }
            }
            return AppError.validation(
              message: 'Please correct the following errors:',
              fieldErrors: fieldErrors,
            );
          }
        }
        return AppError.validation(message: message, fieldErrors: {});

      case 429:
        return AppError.server(
          message: 'Too many requests. Please try again later.',
          statusCode: statusCode,
        );

      case 500:
      case 502:
      case 503:
      case 504:
        return AppError.server(
          message: 'Server error. Please try again later.',
          statusCode: statusCode,
        );

      default:
        return AppError.server(message: message, statusCode: statusCode);
    }
  }

  /// Extracts error message from API response
  static String? _extractErrorMessage(dynamic responseData) {
    if (responseData is Map<String, dynamic>) {
      // Try common error message fields
      return responseData['message'] as String? ??
          responseData['error'] as String? ??
          responseData['detail'] as String?;
    }
    return null;
  }

  /// Returns default error message based on status code
  static String _getDefaultErrorMessage(int statusCode) {
    switch (statusCode) {
      case 400:
        return 'Bad request. Please check your input.';
      case 401:
        return 'Authentication required. Please log in.';
      case 403:
        return 'Access denied. You don\'t have permission to perform this action.';
      case 404:
        return 'Resource not found.';
      case 422:
        return 'Invalid input data.';
      case 429:
        return 'Too many requests. Please try again later.';
      case 500:
        return 'Internal server error. Please try again later.';
      case 502:
        return 'Bad gateway. Please try again later.';
      case 503:
        return 'Service unavailable. Please try again later.';
      case 504:
        return 'Gateway timeout. Please try again later.';
      default:
        return 'An error occurred. Please try again.';
    }
  }

  /// Checks if an error is retryable
  static bool isRetryableError(AppError error) {
    return error.when(
      network: (message, details) => true,
      server: (message, statusCode) => statusCode >= 500 || statusCode == 429,
      authentication: (message, errorCode) => false,
      validation: (message, fieldErrors) => false,
      unknown: (message, exception) => true,
    );
  }

  /// Gets user-friendly error message for display
  static String getUserFriendlyMessage(AppError error) {
    return error.when(
      network: (message, details) => message,
      authentication: (message, errorCode) => message,
      validation: (message, fieldErrors) => message,
      server: (message, statusCode) => message,
      unknown: (message, exception) => message,
    );
  }
}
