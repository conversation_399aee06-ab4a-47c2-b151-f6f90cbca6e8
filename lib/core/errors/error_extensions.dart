import 'package:flutter/material.dart';
import 'app_error.dart';

/// Extensions for AppError to provide additional utility methods
extension AppErrorExtensions on AppError {
  /// Returns true if this error is a network-related error
  bool get isNetworkError => when(
    network: (_, __) => true,
    authentication: (_, __) => false,
    validation: (_, __) => false,
    server: (_, __) => false,
    unknown: (_, __) => false,
  );

  /// Returns true if this error is an authentication error
  bool get isAuthError => when(
    network: (_, __) => false,
    authentication: (_, __) => true,
    validation: (_, __) => false,
    server: (_, __) => false,
    unknown: (_, __) => false,
  );

  /// Returns true if this error is a validation error
  bool get isValidationError => when(
    network: (_, __) => false,
    authentication: (_, __) => false,
    validation: (_, __) => true,
    server: (_, __) => false,
    unknown: (_, __) => false,
  );

  /// Returns true if this error is a server error
  bool get isServerError => when(
    network: (_, __) => false,
    authentication: (_, __) => false,
    validation: (_, __) => false,
    server: (_, __) => true,
    unknown: (_, __) => false,
  );

  /// Returns true if this error can be retried
  bool get canRetry => when(
    network: (_, __) => true,
    authentication: (_, __) => false,
    validation: (_, __) => false,
    server: (message, statusCode) => statusCode >= 500 || statusCode == 429,
    unknown: (_, __) => true,
  );

  /// Returns the appropriate icon for this error type
  IconData get icon => when(
    network: (_, __) => Icons.wifi_off,
    authentication: (_, __) => Icons.lock_outline,
    validation: (_, __) => Icons.error_outline,
    server: (_, __) => Icons.cloud_off,
    unknown: (_, __) => Icons.warning_outlined,
  );

  /// Returns the appropriate color for this error type
  Color getColor(BuildContext context) {
    final theme = Theme.of(context);
    return when(
      network: (_, __) => theme.colorScheme.error,
      authentication: (_, __) => theme.colorScheme.error,
      validation: (_, __) => Colors.orange,
      server: (_, __) => theme.colorScheme.error,
      unknown: (_, __) => theme.colorScheme.error,
    );
  }

  /// Returns field errors for validation errors, empty map otherwise
  Map<String, String> get fieldErrors => when(
    network: (_, __) => {},
    authentication: (_, __) => {},
    validation: (_, fieldErrors) => fieldErrors,
    server: (_, __) => {},
    unknown: (_, __) => {},
  );

  /// Returns the status code for server errors, null otherwise
  int? get statusCode => when(
    network: (_, __) => null,
    authentication: (_, __) => null,
    validation: (_, __) => null,
    server: (_, statusCode) => statusCode,
    unknown: (_, __) => null,
  );

  /// Returns the error code for authentication errors, null otherwise
  String? get errorCode => when(
    network: (_, __) => null,
    authentication: (_, errorCode) => errorCode,
    validation: (_, __) => null,
    server: (_, __) => null,
    unknown: (_, __) => null,
  );

  /// Returns additional details if available
  String? get details => when(
    network: (_, details) => details,
    authentication: (_, __) => null,
    validation: (_, __) => null,
    server: (_, __) => null,
    unknown: (_, exception) => exception?.toString(),
  );

  /// Returns a user-friendly title for the error
  String get title => when(
    network: (_, __) => 'Connection Error',
    authentication: (_, __) => 'Authentication Error',
    validation: (_, __) => 'Validation Error',
    server: (_, __) => 'Server Error',
    unknown: (_, __) => 'Unexpected Error',
  );

  /// Returns a short description of the error type
  String get description => when(
    network: (_, __) => 'Please check your internet connection',
    authentication: (_, __) => 'Please check your credentials',
    validation: (_, __) => 'Please correct the highlighted fields',
    server: (_, __) => 'Something went wrong on our end',
    unknown: (_, __) => 'An unexpected error occurred',
  );
}

/// Extensions for handling errors in async operations
extension FutureErrorHandling<T> on Future<T> {
  /// Catches errors and converts them to AppError
  Future<T> catchAppError() async {
    try {
      return await this;
    } catch (error) {
      throw AppError.unknown(
        message: 'An error occurred during operation',
        exception: error,
      );
    }
  }
}

/// Extensions for Result-like error handling
extension ResultExtensions<T> on T {
  /// Wraps a value in a successful result
  Result<T> toSuccess() => Result.success(this);
}

/// A Result type for handling operations that can fail
sealed class Result<T> {
  const Result();

  /// Creates a successful result
  const factory Result.success(T data) = Success<T>;

  /// Creates a failed result
  const factory Result.failure(AppError error) = Failure<T>;

  /// Returns true if this is a successful result
  bool get isSuccess => this is Success<T>;

  /// Returns true if this is a failed result
  bool get isFailure => this is Failure<T>;

  /// Returns the data if successful, null otherwise
  T? get data => switch (this) {
    Success<T>(data: final data) => data,
    Failure<T>() => null,
  };

  /// Returns the error if failed, null otherwise
  AppError? get error => switch (this) {
    Success<T>() => null,
    Failure<T>(error: final error) => error,
  };

  /// Transforms the data if successful
  Result<R> map<R>(R Function(T data) transform) => switch (this) {
    Success<T>(data: final data) => Result.success(transform(data)),
    Failure<T>(error: final error) => Result.failure(error),
  };

  /// Handles both success and failure cases
  R when<R>({
    required R Function(T data) success,
    required R Function(AppError error) failure,
  }) => switch (this) {
    Success<T>(data: final data) => success(data),
    Failure<T>(error: final error) => failure(error),
  };
}

/// Success result implementation
final class Success<T> extends Result<T> {
  const Success(this.data);

  @override
  final T data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Success<T> &&
          runtimeType == other.runtimeType &&
          data == other.data;

  @override
  int get hashCode => data.hashCode;

  @override
  String toString() => 'Success($data)';
}

/// Failure result implementation
final class Failure<T> extends Result<T> {
  const Failure(this.error);

  @override
  final AppError error;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Failure<T> &&
          runtimeType == other.runtimeType &&
          error == other.error;

  @override
  int get hashCode => error.hashCode;

  @override
  String toString() => 'Failure($error)';
}
