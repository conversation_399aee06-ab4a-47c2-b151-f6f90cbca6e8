/// App spacing system based on design specifications
class AppSpacing {
  // Base spacing values (in logical pixels)
  static const double xs = 4.0;
  static const double sm = 8.0;
  static const double md = 16.0;
  static const double lg = 24.0;
  static const double xl = 32.0;
  static const double xxl = 48.0;
  static const double xxxl = 64.0;

  // Semantic spacing
  static const double tiny = xs;
  static const double small = sm;
  static const double medium = md;
  static const double large = lg;
  static const double extraLarge = xl;
  static const double huge = xxl;
  static const double massive = xxxl;

  // Component-specific spacing
  static const double buttonPadding = md;
  static const double cardPadding = md;
  static const double screenPadding = md;
  static const double sectionSpacing = lg;
  static const double itemSpacing = sm;
  static const double listItemSpacing = sm;

  // Layout spacing
  static const double containerPadding = md;
  static const double contentMargin = md;
  static const double elementSpacing = sm;
  static const double groupSpacing = lg;

  // Form spacing
  static const double formFieldSpacing = md;
  static const double formSectionSpacing = lg;
  static const double formButtonSpacing = xl;

  // Navigation spacing
  static const double tabBarPadding = sm;
  static const double navigationPadding = md;
  static const double headerPadding = md;

  // Modal and dialog spacing
  static const double modalPadding = lg;
  static const double dialogPadding = lg;
  static const double bottomSheetPadding = lg;
}
