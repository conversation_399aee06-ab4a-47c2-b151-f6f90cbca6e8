import 'package:flutter/material.dart';
import 'app_colors.dart';

/// Custom theme extensions for additional design tokens
@immutable
class AppThemeExtensions extends ThemeExtension<AppThemeExtensions> {
  const AppThemeExtensions({
    required this.shadows,
    required this.borderRadius,
    required this.animations,
    required this.layout,
  });

  final AppShadows shadows;
  final AppBorderRadius borderRadius;
  final AppAnimations animations;
  final AppLayout layout;

  @override
  AppThemeExtensions copyWith({
    AppShadows? shadows,
    AppBorderRadius? borderRadius,
    AppAnimations? animations,
    AppLayout? layout,
  }) {
    return AppThemeExtensions(
      shadows: shadows ?? this.shadows,
      borderRadius: borderRadius ?? this.borderRadius,
      animations: animations ?? this.animations,
      layout: layout ?? this.layout,
    );
  }

  @override
  AppThemeExtensions lerp(ThemeExtension<AppThemeExtensions>? other, double t) {
    if (other is! AppThemeExtensions) {
      return this;
    }
    return AppThemeExtensions(
      shadows: shadows.lerp(other.shadows, t),
      borderRadius: borderRadius.lerp(other.borderRadius, t),
      animations: animations.lerp(other.animations, t),
      layout: layout.lerp(other.layout, t),
    );
  }

  static const AppThemeExtensions light = AppThemeExtensions(
    shadows: AppShadows.light,
    borderRadius: AppBorderRadius.standard,
    animations: AppAnimations.standard,
    layout: AppLayout.standard,
  );
}

/// Shadow definitions based on design system
@immutable
class AppShadows {
  const AppShadows({
    required this.small,
    required this.medium,
    required this.large,
    required this.extraLarge,
  });

  final BoxShadow small;
  final BoxShadow medium;
  final BoxShadow large;
  final BoxShadow extraLarge;

  static const AppShadows light = AppShadows(
    small: BoxShadow(
      color: Color(0x0D000000),
      offset: Offset(0, 1),
      blurRadius: 2,
      spreadRadius: 0,
    ),
    medium: BoxShadow(
      color: Color(0x1A000000),
      offset: Offset(0, 4),
      blurRadius: 6,
      spreadRadius: -1,
    ),
    large: BoxShadow(
      color: Color(0x1A000000),
      offset: Offset(0, 10),
      blurRadius: 15,
      spreadRadius: -3,
    ),
    extraLarge: BoxShadow(
      color: Color(0x1A000000),
      offset: Offset(0, 20),
      blurRadius: 25,
      spreadRadius: -5,
    ),
  );

  AppShadows lerp(AppShadows other, double t) {
    return AppShadows(
      small: BoxShadow.lerp(small, other.small, t) ?? small,
      medium: BoxShadow.lerp(medium, other.medium, t) ?? medium,
      large: BoxShadow.lerp(large, other.large, t) ?? large,
      extraLarge: BoxShadow.lerp(extraLarge, other.extraLarge, t) ?? extraLarge,
    );
  }
}

/// Border radius definitions
@immutable
class AppBorderRadius {
  const AppBorderRadius({
    required this.none,
    required this.small,
    required this.medium,
    required this.large,
    required this.extraLarge,
    required this.extraExtraLarge,
    required this.full,
  });

  final BorderRadius none;
  final BorderRadius small;
  final BorderRadius medium;
  final BorderRadius large;
  final BorderRadius extraLarge;
  final BorderRadius extraExtraLarge;
  final BorderRadius full;

  static const AppBorderRadius standard = AppBorderRadius(
    none: BorderRadius.zero,
    small: BorderRadius.all(Radius.circular(4)),
    medium: BorderRadius.all(Radius.circular(8)),
    large: BorderRadius.all(Radius.circular(12)),
    extraLarge: BorderRadius.all(Radius.circular(16)),
    extraExtraLarge: BorderRadius.all(Radius.circular(24)),
    full: BorderRadius.all(Radius.circular(9999)),
  );

  AppBorderRadius lerp(AppBorderRadius other, double t) {
    return AppBorderRadius(
      none: BorderRadius.lerp(none, other.none, t) ?? none,
      small: BorderRadius.lerp(small, other.small, t) ?? small,
      medium: BorderRadius.lerp(medium, other.medium, t) ?? medium,
      large: BorderRadius.lerp(large, other.large, t) ?? large,
      extraLarge:
          BorderRadius.lerp(extraLarge, other.extraLarge, t) ?? extraLarge,
      extraExtraLarge:
          BorderRadius.lerp(extraExtraLarge, other.extraExtraLarge, t) ??
          extraExtraLarge,
      full: BorderRadius.lerp(full, other.full, t) ?? full,
    );
  }
}

/// Animation definitions
@immutable
class AppAnimations {
  const AppAnimations({
    required this.fast,
    required this.medium,
    required this.slow,
    required this.curve,
  });

  final Duration fast;
  final Duration medium;
  final Duration slow;
  final Curve curve;

  static const AppAnimations standard = AppAnimations(
    fast: Duration(milliseconds: 150),
    medium: Duration(milliseconds: 300),
    slow: Duration(milliseconds: 500),
    curve: Curves.easeInOut,
  );

  AppAnimations lerp(AppAnimations other, double t) {
    return AppAnimations(
      fast: Duration(
        milliseconds:
            (fast.inMilliseconds +
                    (other.fast.inMilliseconds - fast.inMilliseconds) * t)
                .round(),
      ),
      medium: Duration(
        milliseconds:
            (medium.inMilliseconds +
                    (other.medium.inMilliseconds - medium.inMilliseconds) * t)
                .round(),
      ),
      slow: Duration(
        milliseconds:
            (slow.inMilliseconds +
                    (other.slow.inMilliseconds - slow.inMilliseconds) * t)
                .round(),
      ),
      curve: curve, // Curves don't interpolate well, so we keep the original
    );
  }
}

/// Layout definitions
@immutable
class AppLayout {
  const AppLayout({
    required this.maxMobileWidth,
    required this.statusBarHeight,
    required this.tabBarHeight,
    required this.headerHeight,
    required this.minTouchTarget,
  });

  final double maxMobileWidth;
  final double statusBarHeight;
  final double tabBarHeight;
  final double headerHeight;
  final double minTouchTarget;

  static const AppLayout standard = AppLayout(
    maxMobileWidth: 414.0,
    statusBarHeight: 44.0,
    tabBarHeight: 83.0,
    headerHeight: 56.0,
    minTouchTarget: 44.0,
  );

  AppLayout lerp(AppLayout other, double t) {
    return AppLayout(
      maxMobileWidth:
          maxMobileWidth + (other.maxMobileWidth - maxMobileWidth) * t,
      statusBarHeight:
          statusBarHeight + (other.statusBarHeight - statusBarHeight) * t,
      tabBarHeight: tabBarHeight + (other.tabBarHeight - tabBarHeight) * t,
      headerHeight: headerHeight + (other.headerHeight - headerHeight) * t,
      minTouchTarget:
          minTouchTarget + (other.minTouchTarget - minTouchTarget) * t,
    );
  }
}

/// Extension to easily access custom theme extensions
extension ThemeDataExtensions on ThemeData {
  AppThemeExtensions get appExtensions {
    return extension<AppThemeExtensions>() ?? AppThemeExtensions.light;
  }
}

/// Utility class for common component decorations
class AppDecorations {
  static BoxDecoration get card => BoxDecoration(
    color: AppColors.surface,
    borderRadius: AppBorderRadius.standard.large,
    boxShadow: [AppShadows.light.medium],
  );

  static BoxDecoration get modal => BoxDecoration(
    color: AppColors.surface,
    borderRadius: AppBorderRadius.standard.extraLarge,
    boxShadow: [AppShadows.light.extraLarge],
  );

  static BoxDecoration get bottomSheet => BoxDecoration(
    color: AppColors.surface,
    borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
    boxShadow: [AppShadows.light.extraLarge],
  );

  static BoxDecoration get input => BoxDecoration(
    color: AppColors.backgroundSecondary,
    borderRadius: AppBorderRadius.standard.large,
    border: Border.all(color: AppColors.border),
  );

  static BoxDecoration get inputFocused => BoxDecoration(
    color: AppColors.backgroundSecondary,
    borderRadius: AppBorderRadius.standard.large,
    border: Border.all(color: AppColors.borderFocus, width: 2),
  );

  static BoxDecoration get inputError => BoxDecoration(
    color: AppColors.backgroundSecondary,
    borderRadius: AppBorderRadius.standard.large,
    border: Border.all(color: AppColors.borderError),
  );
}
