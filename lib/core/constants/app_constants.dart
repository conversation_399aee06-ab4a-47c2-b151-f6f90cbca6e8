class AppConstants {
  // API Configuration
  static const String baseUrl = 'http://localhost:8000/api/v1';
  static const String apiVersion = 'v1';

  // API Endpoints
  static const String authRegister = '/auth/register';
  static const String authLogin = '/auth/login';
  static const String authLogout = '/auth/logout';
  static const String authProfile = '/auth/profile';
  static const String authVerifyToken = '/auth/verify-token';
  static const String authProfileImage = '/auth/profile/image';
  static const String ridersProfile = '/riders/profile';
  static const String driversProfile = '/drivers/profile';
  static const String driversVehicle = '/drivers/vehicle';

  // Storage Keys
  static const String tokenKey = 'auth_token';
  static const String userDataKey = 'user_data';
  static const String languageKey = 'language';
  static const String themeKey = 'theme_mode';
  static const String onboardingKey = 'onboarding_completed';

  // App Configuration
  static const String appName = 'Lucian Rides';
  static const String appVersion = '1.0.0';
  static const Duration requestTimeout = Duration(seconds: 30);
  static const Duration connectTimeout = Duration(seconds: 15);
  static const Duration animationDuration = Duration(milliseconds: 300);

  // Validation
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 128;
  static const int maxNameLength = 50;
  static const int maxPhoneLength = 15;
  static const int maxEmailLength = 254;

  // UI Constants
  static const double minTouchTarget = 44.0;
  static const double defaultPadding = 16.0;
  static const double defaultMargin = 16.0;
  static const double defaultBorderRadius = 12.0;
  static const double cardElevation = 4.0;

  // Layout Constants
  static const double maxMobileWidth = 414.0;
  static const double statusBarHeight = 44.0;
  static const double tabBarHeight = 83.0;
  static const double headerHeight = 56.0;
  static const double bottomSheetHandleHeight = 4.0;
  static const double bottomSheetHandleWidth = 36.0;

  // Animation Constants
  static const double buttonScaleDown = 0.95;
  static const double cardHoverScale = 1.02;

  // Error Messages
  static const String networkErrorMessage =
      'Network connection error. Please check your internet connection.';
  static const String serverErrorMessage =
      'Server error occurred. Please try again later.';
  static const String unknownErrorMessage =
      'An unexpected error occurred. Please try again.';
  static const String authenticationErrorMessage =
      'Authentication failed. Please login again.';
  static const String validationErrorMessage =
      'Please check your input and try again.';

  // Success Messages
  static const String loginSuccessMessage = 'Login successful!';
  static const String registerSuccessMessage = 'Registration successful!';
  static const String profileUpdateSuccessMessage =
      'Profile updated successfully!';
  static const String logoutSuccessMessage = 'Logged out successfully!';

  // Regex Patterns
  static const String emailPattern =
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static const String phonePattern = r'^\+?[1-9]\d{1,14}$';
  static const String namePattern = r'^[a-zA-Z\s]{2,50}$';

  // Environment Configuration
  static const bool isProduction = bool.fromEnvironment('dart.vm.product');
  static const bool enableLogging = !isProduction;
  static const bool enableDebugMode = !isProduction;
}
