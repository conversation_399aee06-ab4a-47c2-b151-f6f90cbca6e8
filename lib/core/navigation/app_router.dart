import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';

import '../../presentation/screens/welcome_screen.dart';
import '../../presentation/screens/login_screen.dart';
import '../../presentation/screens/register_screen.dart';
import '../../presentation/screens/splash_screen.dart';
import '../../presentation/screens/dashboard_screen.dart';
import '../../presentation/screens/profile_screen.dart';
import '../../presentation/screens/profile_setup_screen.dart';
import '../../presentation/screens/edit_profile_screen.dart';
import '../../domain/entities/user.dart';

part 'app_router.gr.dart';

/// AutoRoute configuration for the Lucian Rides app
/// Defines all routes and navigation structure
@AutoRouterConfig()
class AppRouter extends _$AppRouter {
  @override
  List<AutoRoute> get routes => [
    // Splash screen - initial route
    AutoRoute(page: SplashRoute.page, path: '/', initial: true),

    // Authentication routes
    AutoRoute(page: WelcomeRoute.page, path: '/welcome'),
    AutoRoute(page: LoginRoute.page, path: '/login'),
    AutoRoute(page: RegisterRoute.page, path: '/register'),

    // Main app routes - protected by authentication guard
    AutoRoute(
      page: DashboardRoute.page,
      path: '/dashboard',
      guards: [AuthGuard()],
      children: [
        // Nested routes for dashboard tabs will be added in subtask 5.2
      ],
    ),

    // Profile routes - protected by authentication guard
    AutoRoute(page: ProfileRoute.page, path: '/profile', guards: [AuthGuard()]),
    AutoRoute(
      page: ProfileSetupRoute.page,
      path: '/profile/setup',
      guards: [AuthGuard()],
    ),
    AutoRoute(
      page: EditProfileRoute.page,
      path: '/profile/edit',
      guards: [AuthGuard()],
    ),
  ];
}

/// Authentication guard to protect routes that require authentication
class AuthGuard extends AutoRouteGuard {
  @override
  void onNavigation(NavigationResolver resolver, StackRouter router) {
    // Check if user is authenticated
    // For now, we'll implement a basic check - this will be enhanced with proper state management
    // In a real implementation, you would check the authentication state from your provider

    // Allow navigation for now - the splash screen will handle proper authentication flow
    // The guard will be fully implemented when we have proper auth state management
    resolver.next();

    // Future implementation would look like:
    // final container = ProviderScope.containerOf(router.navigatorKey.currentContext!);
    // final authState = container.read(authStateProvider);
    //
    // authState.when(
    //   authenticated: (_) => resolver.next(),
    //   unauthenticated: () => router.pushAndClearStack(const WelcomeRoute()),
    //   initial: () => router.pushAndClearStack(const SplashRoute()),
    //   loading: () => router.pushAndClearStack(const SplashRoute()),
    //   error: (_, __) => router.pushAndClearStack(const WelcomeRoute()),
    // );
  }
}
