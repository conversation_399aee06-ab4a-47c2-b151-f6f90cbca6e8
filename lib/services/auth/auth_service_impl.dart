import '../../domain/entities/user.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../data/datasources/storage_service.dart';
import '../../data/models/auth_result.dart';
import '../../core/errors/app_error.dart';
import 'auth_service.dart';

/// Implementation of AuthService with comprehensive error handling and token management
class AuthServiceImpl implements AuthService {
  final AuthRepository _authRepository;
  final StorageService _storageService;

  AuthServiceImpl({
    required AuthRepository authRepository,
    required StorageService storageService,
  }) : _authRepository = authRepository,
       _storageService = storageService;

  @override
  Future<AuthResult> login(String email, String password) async {
    try {
      // Validate input parameters
      if (email.isEmpty || password.isEmpty) {
        return const AuthResult.failure(
          message: 'Email and password are required',
          errorCode: 'INVALID_INPUT',
        );
      }

      // Perform login through repository
      final token = await _authRepository.login(email, password);

      // Get user data after successful login
      final user = await _authRepository.getCurrentUser();

      return AuthResult.success(token: token, user: user);
    } on AppError catch (e) {
      // Handle known app errors
      return AuthResult.failure(
        message: e.message,
        errorCode: e.when(
          network: (message, details) => 'NETWORK_ERROR',
          authentication: (message, errorCode) => errorCode ?? 'AUTH_ERROR',
          validation: (message, fieldErrors) => 'VALIDATION_ERROR',
          server: (message, statusCode) => 'SERVER_ERROR',
          unknown: (message, exception) => 'UNKNOWN_ERROR',
        ),
      );
    } catch (e) {
      // Handle unexpected errors
      return AuthResult.failure(
        message: 'Login failed: ${e.toString()}',
        errorCode: 'UNKNOWN_ERROR',
      );
    }
  }

  @override
  Future<AuthResult> register({
    required String email,
    required String password,
    required String name,
    required UserType userType,
    String? phone,
  }) async {
    try {
      // Validate input parameters
      if (email.isEmpty || password.isEmpty || name.isEmpty) {
        return const AuthResult.failure(
          message: 'Email, password, and name are required',
          errorCode: 'INVALID_INPUT',
        );
      }

      // Perform registration through repository
      final token = await _authRepository.register(
        email: email,
        password: password,
        name: name,
        userType: userType,
        phone: phone,
      );

      // Get user data after successful registration
      final user = await _authRepository.getCurrentUser();

      return AuthResult.success(token: token, user: user);
    } on AppError catch (e) {
      // Handle known app errors
      return AuthResult.failure(
        message: e.message,
        errorCode: e.when(
          network: (message, details) => 'NETWORK_ERROR',
          authentication: (message, errorCode) => errorCode ?? 'AUTH_ERROR',
          validation: (message, fieldErrors) => 'VALIDATION_ERROR',
          server: (message, statusCode) => 'SERVER_ERROR',
          unknown: (message, exception) => 'UNKNOWN_ERROR',
        ),
      );
    } catch (e) {
      // Handle unexpected errors
      return AuthResult.failure(
        message: 'Registration failed: ${e.toString()}',
        errorCode: 'UNKNOWN_ERROR',
      );
    }
  }

  @override
  Future<void> logout() async {
    try {
      // Perform logout through repository (clears storage and notifies server)
      await _authRepository.logout();
    } catch (e) {
      // Even if logout fails, we should clear local data
      await _storageService.clearAll();
      rethrow;
    }
  }

  @override
  Future<User?> getCurrentUser() async {
    try {
      // Check if user is authenticated before fetching user data
      if (await isAuthenticated()) {
        return await _authRepository.getCurrentUser();
      }
      return null;
    } catch (e) {
      // If getting current user fails, clear potentially corrupted data
      await _storageService.clearUserData();
      return null;
    }
  }

  @override
  Future<bool> verifyToken() async {
    try {
      return await _authRepository.verifyToken();
    } catch (e) {
      // If token verification fails, clear invalid token
      await _storageService.clearToken();
      await _storageService.clearUserData();
      return false;
    }
  }

  @override
  Future<bool> isAuthenticated() async {
    try {
      // First check if token exists locally
      final hasToken = await _storageService.hasToken();
      if (!hasToken) return false;

      // Then verify token with server
      return await verifyToken();
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> refreshToken() async {
    try {
      // Check if we have a current token to refresh
      final currentToken = await _storageService.getToken();
      if (currentToken == null) return false;

      // Note: This would typically call a refresh endpoint
      // For now, we'll verify the current token as a placeholder
      // In a real implementation, you'd call something like:
      // final newToken = await _apiClient.post('/auth/refresh', data: {'token': currentToken});

      // Verify current token is still valid
      return await verifyToken();
    } catch (e) {
      return false;
    }
  }

  @override
  Future<String?> getStoredToken() async {
    try {
      return await _storageService.getToken();
    } catch (e) {
      return null;
    }
  }

  @override
  Future<bool> hasValidSession() async {
    try {
      // Only check local token existence without server verification
      return await _storageService.hasToken();
    } catch (e) {
      return false;
    }
  }
}
