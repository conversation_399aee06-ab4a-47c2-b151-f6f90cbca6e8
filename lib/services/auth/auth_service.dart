import '../../domain/entities/user.dart';
import '../../data/models/auth_result.dart';

/// Authentication service interface providing comprehensive user authentication operations
/// Handles login, registration, logout, token verification, and user session management
abstract class AuthService {
  /// Authenticate user with email and password
  /// Returns AuthResult with success/failure status and user data or error message
  Future<AuthResult> login(String email, String password);

  /// Register new user with required information
  /// Returns AuthResult with success/failure status and user data or error message
  Future<AuthResult> register({
    required String email,
    required String password,
    required String name,
    required UserType userType,
    String? phone,
  });

  /// Logout current user and clear all stored authentication data
  /// Notifies server and clears local storage regardless of server response
  Future<void> logout();

  /// Get currently authenticated user
  /// Returns null if no user is authenticated or token is invalid
  Future<User?> getCurrentUser();

  /// Verify stored authentication token with server
  /// Returns true if token is valid, false otherwise
  /// Automatically clears invalid tokens
  Future<bool> verifyToken();

  /// Check if user is currently authenticated
  /// Verifies both local token existence and server validation
  Future<bool> isAuthenticated();

  /// Refresh authentication token if supported by backend
  /// Returns true if refresh was successful, false otherwise
  Future<bool> refreshToken();

  /// Get stored authentication token
  /// Returns null if no token is stored
  Future<String?> getStoredToken();

  /// Check if user session is valid without server call
  /// Only checks local token existence
  Future<bool> hasValidSession();
}
