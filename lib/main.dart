import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'core/di/service_locator.dart';
import 'core/theme/app_theme.dart';
import 'core/constants/app_constants.dart';
import 'core/navigation/app_router.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize dependency injection
  await setupServiceLocator();

  runApp(const ProviderScope(child: <PERSON><PERSON><PERSON><PERSON><PERSON>()));
}

class LucianRidesApp extends ConsumerWidget {
  const LucianRidesApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Create the app router instance
    final appRouter = AppRouter();

    return MaterialApp.router(
      title: AppConstants.appName,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      debugShowCheckedModeBanner: false,
      // Use AutoRoute for navigation
      routerConfig: appRouter.config(),
    );
  }
}
