// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'rider_profile.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RiderProfileImpl _$$RiderProfileImplFromJson(Map<String, dynamic> json) =>
    _$RiderProfileImpl(
      id: json['id'] as String,
      userId: json['userId'] as String,
      emergencyContactName: json['emergencyContactName'] as String?,
      emergencyContactPhone: json['emergencyContactPhone'] as String?,
      preferredLanguage: json['preferredLanguage'] as String?,
      frequentDestinations: (json['frequentDestinations'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      isProfileComplete: json['isProfileComplete'] as bool? ?? false,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$RiderProfileImplToJson(_$RiderProfileImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      if (instance.emergencyContactName case final value?)
        'emergencyContactName': value,
      if (instance.emergencyContactPhone case final value?)
        'emergencyContactPhone': value,
      if (instance.preferredLanguage case final value?)
        'preferredLanguage': value,
      'frequentDestinations': instance.frequentDestinations,
      'isProfileComplete': instance.isProfileComplete,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };
