// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'rider_profile.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RiderProfile _$RiderProfileFromJson(Map<String, dynamic> json) {
  return _RiderProfile.fromJson(json);
}

/// @nodoc
mixin _$RiderProfile {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String? get emergencyContactName => throw _privateConstructorUsedError;
  String? get emergencyContactPhone => throw _privateConstructorUsedError;
  String? get preferredLanguage => throw _privateConstructorUsedError;
  List<String> get frequentDestinations => throw _privateConstructorUsedError;
  bool get isProfileComplete => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this RiderProfile to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RiderProfile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RiderProfileCopyWith<RiderProfile> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RiderProfileCopyWith<$Res> {
  factory $RiderProfileCopyWith(
          RiderProfile value, $Res Function(RiderProfile) then) =
      _$RiderProfileCopyWithImpl<$Res, RiderProfile>;
  @useResult
  $Res call(
      {String id,
      String userId,
      String? emergencyContactName,
      String? emergencyContactPhone,
      String? preferredLanguage,
      List<String> frequentDestinations,
      bool isProfileComplete,
      DateTime createdAt,
      DateTime updatedAt});
}

/// @nodoc
class _$RiderProfileCopyWithImpl<$Res, $Val extends RiderProfile>
    implements $RiderProfileCopyWith<$Res> {
  _$RiderProfileCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RiderProfile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? emergencyContactName = freezed,
    Object? emergencyContactPhone = freezed,
    Object? preferredLanguage = freezed,
    Object? frequentDestinations = null,
    Object? isProfileComplete = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      emergencyContactName: freezed == emergencyContactName
          ? _value.emergencyContactName
          : emergencyContactName // ignore: cast_nullable_to_non_nullable
              as String?,
      emergencyContactPhone: freezed == emergencyContactPhone
          ? _value.emergencyContactPhone
          : emergencyContactPhone // ignore: cast_nullable_to_non_nullable
              as String?,
      preferredLanguage: freezed == preferredLanguage
          ? _value.preferredLanguage
          : preferredLanguage // ignore: cast_nullable_to_non_nullable
              as String?,
      frequentDestinations: null == frequentDestinations
          ? _value.frequentDestinations
          : frequentDestinations // ignore: cast_nullable_to_non_nullable
              as List<String>,
      isProfileComplete: null == isProfileComplete
          ? _value.isProfileComplete
          : isProfileComplete // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RiderProfileImplCopyWith<$Res>
    implements $RiderProfileCopyWith<$Res> {
  factory _$$RiderProfileImplCopyWith(
          _$RiderProfileImpl value, $Res Function(_$RiderProfileImpl) then) =
      __$$RiderProfileImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      String? emergencyContactName,
      String? emergencyContactPhone,
      String? preferredLanguage,
      List<String> frequentDestinations,
      bool isProfileComplete,
      DateTime createdAt,
      DateTime updatedAt});
}

/// @nodoc
class __$$RiderProfileImplCopyWithImpl<$Res>
    extends _$RiderProfileCopyWithImpl<$Res, _$RiderProfileImpl>
    implements _$$RiderProfileImplCopyWith<$Res> {
  __$$RiderProfileImplCopyWithImpl(
      _$RiderProfileImpl _value, $Res Function(_$RiderProfileImpl) _then)
      : super(_value, _then);

  /// Create a copy of RiderProfile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? emergencyContactName = freezed,
    Object? emergencyContactPhone = freezed,
    Object? preferredLanguage = freezed,
    Object? frequentDestinations = null,
    Object? isProfileComplete = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_$RiderProfileImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      emergencyContactName: freezed == emergencyContactName
          ? _value.emergencyContactName
          : emergencyContactName // ignore: cast_nullable_to_non_nullable
              as String?,
      emergencyContactPhone: freezed == emergencyContactPhone
          ? _value.emergencyContactPhone
          : emergencyContactPhone // ignore: cast_nullable_to_non_nullable
              as String?,
      preferredLanguage: freezed == preferredLanguage
          ? _value.preferredLanguage
          : preferredLanguage // ignore: cast_nullable_to_non_nullable
              as String?,
      frequentDestinations: null == frequentDestinations
          ? _value._frequentDestinations
          : frequentDestinations // ignore: cast_nullable_to_non_nullable
              as List<String>,
      isProfileComplete: null == isProfileComplete
          ? _value.isProfileComplete
          : isProfileComplete // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RiderProfileImpl implements _RiderProfile {
  const _$RiderProfileImpl(
      {required this.id,
      required this.userId,
      this.emergencyContactName,
      this.emergencyContactPhone,
      this.preferredLanguage,
      final List<String> frequentDestinations = const [],
      this.isProfileComplete = false,
      required this.createdAt,
      required this.updatedAt})
      : _frequentDestinations = frequentDestinations;

  factory _$RiderProfileImpl.fromJson(Map<String, dynamic> json) =>
      _$$RiderProfileImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String? emergencyContactName;
  @override
  final String? emergencyContactPhone;
  @override
  final String? preferredLanguage;
  final List<String> _frequentDestinations;
  @override
  @JsonKey()
  List<String> get frequentDestinations {
    if (_frequentDestinations is EqualUnmodifiableListView)
      return _frequentDestinations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_frequentDestinations);
  }

  @override
  @JsonKey()
  final bool isProfileComplete;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;

  @override
  String toString() {
    return 'RiderProfile(id: $id, userId: $userId, emergencyContactName: $emergencyContactName, emergencyContactPhone: $emergencyContactPhone, preferredLanguage: $preferredLanguage, frequentDestinations: $frequentDestinations, isProfileComplete: $isProfileComplete, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RiderProfileImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.emergencyContactName, emergencyContactName) ||
                other.emergencyContactName == emergencyContactName) &&
            (identical(other.emergencyContactPhone, emergencyContactPhone) ||
                other.emergencyContactPhone == emergencyContactPhone) &&
            (identical(other.preferredLanguage, preferredLanguage) ||
                other.preferredLanguage == preferredLanguage) &&
            const DeepCollectionEquality()
                .equals(other._frequentDestinations, _frequentDestinations) &&
            (identical(other.isProfileComplete, isProfileComplete) ||
                other.isProfileComplete == isProfileComplete) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      userId,
      emergencyContactName,
      emergencyContactPhone,
      preferredLanguage,
      const DeepCollectionEquality().hash(_frequentDestinations),
      isProfileComplete,
      createdAt,
      updatedAt);

  /// Create a copy of RiderProfile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RiderProfileImplCopyWith<_$RiderProfileImpl> get copyWith =>
      __$$RiderProfileImplCopyWithImpl<_$RiderProfileImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RiderProfileImplToJson(
      this,
    );
  }
}

abstract class _RiderProfile implements RiderProfile {
  const factory _RiderProfile(
      {required final String id,
      required final String userId,
      final String? emergencyContactName,
      final String? emergencyContactPhone,
      final String? preferredLanguage,
      final List<String> frequentDestinations,
      final bool isProfileComplete,
      required final DateTime createdAt,
      required final DateTime updatedAt}) = _$RiderProfileImpl;

  factory _RiderProfile.fromJson(Map<String, dynamic> json) =
      _$RiderProfileImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String? get emergencyContactName;
  @override
  String? get emergencyContactPhone;
  @override
  String? get preferredLanguage;
  @override
  List<String> get frequentDestinations;
  @override
  bool get isProfileComplete;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;

  /// Create a copy of RiderProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RiderProfileImplCopyWith<_$RiderProfileImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
