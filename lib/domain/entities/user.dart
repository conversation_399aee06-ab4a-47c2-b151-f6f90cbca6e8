// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'user.freezed.dart';
part 'user.g.dart';

enum UserType {
  @JsonValue('rider')
  rider,
  @JsonValue('driver')
  driver,
}

extension UserTypeExtension on UserType {
  String get name {
    switch (this) {
      case UserType.rider:
        return 'Rider';
      case UserType.driver:
        return 'Driver';
    }
  }

  String get value {
    switch (this) {
      case UserType.rider:
        return 'rider';
      case UserType.driver:
        return 'driver';
    }
  }
}

@freezed
class User with _$User {
  const factory User({
    required String id,
    required String email,
    required String name,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'user_type') required UserType userType,
    String? phone,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'profile_image') String? profileImage,
    @J<PERSON><PERSON><PERSON>(name: 'created_at') required DateTime createdAt,
    @Json<PERSON>ey(name: 'updated_at') required DateTime updatedAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'is_active') required bool isActive,
    @Default('en') String language,
  }) = _User;

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
}
