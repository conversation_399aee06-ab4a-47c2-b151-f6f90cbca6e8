import 'package:freezed_annotation/freezed_annotation.dart';

part 'rider_profile.freezed.dart';
part 'rider_profile.g.dart';

/// Rider-specific profile information
@freezed
class RiderProfile with _$RiderProfile {
  const factory RiderProfile({
    required String id,
    required String userId,
    String? emergencyContactName,
    String? emergencyContactPhone,
    String? preferredLanguage,
    @Default([]) List<String> frequentDestinations,
    @Default(false) bool isProfileComplete,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) = _RiderProfile;

  factory RiderProfile.fromJson(Map<String, dynamic> json) =>
      _$RiderProfileFromJson(json);
}

/// Extension to check profile completion status
extension RiderProfileValidation on RiderProfile {
  /// Check if the rider profile has all required information
  bool get hasRequiredInfo {
    return emergencyContactName != null &&
        emergencyContactPhone != null &&
        preferredLanguage != null;
  }

  /// Get list of missing required fields
  List<String> get missingRequiredFields {
    final missing = <String>[];

    if (emergencyContactName == null || emergencyContactName!.isEmpty) {
      missing.add('Emergency Contact Name');
    }
    if (emergencyContactPhone == null || emergencyContactPhone!.isEmpty) {
      missing.add('Emergency Contact Phone');
    }
    if (preferredLanguage == null || preferredLanguage!.isEmpty) {
      missing.add('Preferred Language');
    }

    return missing;
  }

  /// Calculate profile completion percentage
  double get completionPercentage {
    int totalFields = 3; // Required fields count
    int completedFields = 0;

    if (emergencyContactName != null && emergencyContactName!.isNotEmpty) {
      completedFields++;
    }
    if (emergencyContactPhone != null && emergencyContactPhone!.isNotEmpty) {
      completedFields++;
    }
    if (preferredLanguage != null && preferredLanguage!.isNotEmpty) {
      completedFields++;
    }

    return completedFields / totalFields;
  }
}
