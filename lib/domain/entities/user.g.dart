// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserImpl _$$UserImplFromJson(Map<String, dynamic> json) => _$UserImpl(
      id: json['id'] as String,
      email: json['email'] as String,
      name: json['name'] as String,
      userType: $enumDecode(_$UserTypeEnumMap, json['user_type']),
      phone: json['phone'] as String?,
      profileImage: json['profile_image'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      isActive: json['is_active'] as bool,
      language: json['language'] as String? ?? 'en',
    );

Map<String, dynamic> _$$UserImplToJson(_$UserImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'name': instance.name,
      'user_type': _$UserTypeEnumMap[instance.userType]!,
      if (instance.phone case final value?) 'phone': value,
      if (instance.profileImage case final value?) 'profile_image': value,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'is_active': instance.isActive,
      'language': instance.language,
    };

const _$UserTypeEnumMap = {
  UserType.rider: 'rider',
  UserType.driver: 'driver',
};
