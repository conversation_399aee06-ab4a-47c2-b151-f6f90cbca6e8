import '../entities/rider_profile.dart';

/// Repository interface for profile management operations (Rider-only app)
abstract class ProfileRepository {
  // Rider profile operations
  Future<RiderProfile?> getRiderProfile();
  Future<RiderProfile> createRiderProfile(RiderProfile profile);
  Future<RiderProfile> updateRiderProfile(RiderProfile profile);

  // Profile image operations
  Future<String> uploadProfileImage(String imagePath);
  Future<void> deleteProfileImage();
}
