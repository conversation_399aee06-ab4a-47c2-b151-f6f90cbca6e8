import 'package:flutter/material.dart';

/// A widget that displays loading state with optional message
class LoadingWidget extends StatelessWidget {
  const LoadingWidget({
    super.key,
    this.message,
    this.size = 24.0,
    this.padding = const EdgeInsets.all(16.0),
  });

  final String? message;
  final double size;
  final EdgeInsets padding;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: size,
            height: size,
            child: const CircularProgressIndicator(),
          ),
          if (message != null) ...[
            const SizedBox(height: 16),
            Text(
              message!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

/// A compact loading widget for inline display
class InlineLoadingWidget extends StatelessWidget {
  const InlineLoadingWidget({super.key, this.message, this.size = 16.0});

  final String? message;
  final double size;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: size,
          height: size,
          child: const CircularProgressIndicator(strokeWidth: 2),
        ),
        if (message != null) ...[
          const SizedBox(width: 8),
          Text(message!, style: Theme.of(context).textTheme.bodyMedium),
        ],
      ],
    );
  }
}

/// A widget that displays success state with optional message
class SuccessWidget extends StatelessWidget {
  const SuccessWidget({
    super.key,
    this.message,
    this.showIcon = true,
    this.padding = const EdgeInsets.all(16.0),
  });

  final String? message;
  final bool showIcon;
  final EdgeInsets padding;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: padding,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (showIcon) ...[
            Icon(Icons.check_circle, size: 48, color: Colors.green[600]),
            const SizedBox(height: 16),
          ],
          if (message != null)
            Text(
              message!,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: Colors.green[700],
              ),
              textAlign: TextAlign.center,
            ),
        ],
      ),
    );
  }
}

/// A snackbar for showing success messages
class SuccessSnackBar extends SnackBar {
  SuccessSnackBar({
    super.key,
    required String message,
    super.duration = const Duration(seconds: 3),
  }) : super(
         content: Row(
           children: [
             const Icon(Icons.check_circle, color: Colors.white, size: 20),
             const SizedBox(width: 12),
             Expanded(
               child: Text(
                 message,
                 style: const TextStyle(color: Colors.white),
               ),
             ),
           ],
         ),
         backgroundColor: Colors.green[600],
       );
}

/// A widget that handles async state display (loading, error, success, data)
class AsyncStateWidget<T> extends StatelessWidget {
  const AsyncStateWidget({
    super.key,
    required this.state,
    required this.dataBuilder,
    this.loadingBuilder,
    this.errorBuilder,
    this.emptyBuilder,
    this.loadingMessage,
    this.onRetry,
  });

  final AsyncValue<T> state;
  final Widget Function(T data) dataBuilder;
  final Widget Function()? loadingBuilder;
  final Widget Function(Object error, VoidCallback? onRetry)? errorBuilder;
  final Widget Function()? emptyBuilder;
  final String? loadingMessage;
  final VoidCallback? onRetry;

  @override
  Widget build(BuildContext context) {
    return state.when(
      data: (data) {
        // Handle empty data case
        if (data == null ||
            (data is List && (data as List).isEmpty) ||
            (data is Map && (data as Map).isEmpty)) {
          return emptyBuilder != null
              ? emptyBuilder!()
              : const Center(child: Text('No data available'));
        }
        return dataBuilder(data);
      },
      loading: () =>
          loadingBuilder?.call() ?? LoadingWidget(message: loadingMessage),
      error: (error, stackTrace) =>
          errorBuilder?.call(error, onRetry) ??
          Center(child: Text('Error: $error')),
    );
  }
}

/// Represents the state of an async operation
sealed class AsyncValue<T> {
  const AsyncValue();

  /// Creates a loading state
  const factory AsyncValue.loading() = AsyncLoading<T>;

  /// Creates a data state
  const factory AsyncValue.data(T data) = AsyncData<T>;

  /// Creates an error state
  const factory AsyncValue.error(Object error, [StackTrace? stackTrace]) =
      AsyncError<T>;

  /// Returns true if this is a loading state
  bool get isLoading => this is AsyncLoading<T>;

  /// Returns true if this is a data state
  bool get hasData => this is AsyncData<T>;

  /// Returns true if this is an error state
  bool get hasError => this is AsyncError<T>;

  /// Returns the data if available, null otherwise
  T? get data => switch (this) {
    AsyncData<T>(data: final data) => data,
    _ => null,
  };

  /// Returns the error if available, null otherwise
  Object? get error => switch (this) {
    AsyncError<T>(error: final error) => error,
    _ => null,
  };

  /// Handles all possible states
  R when<R>({
    required R Function() loading,
    required R Function(T data) data,
    required R Function(Object error, StackTrace? stackTrace) error,
  }) => switch (this) {
    AsyncLoading<T>() => loading(),
    AsyncData<T>(data: final dataValue) => data(dataValue),
    AsyncError<T>(error: final errorValue, stackTrace: final stackTrace) =>
      error(errorValue, stackTrace),
  };

  /// Maps the data if available
  AsyncValue<R> map<R>(R Function(T data) transform) => switch (this) {
    AsyncLoading<T>() => const AsyncValue.loading(),
    AsyncData<T>(data: final data) => AsyncValue.data(transform(data)),
    AsyncError<T>(error: final error, stackTrace: final stackTrace) =>
      AsyncValue.error(error, stackTrace),
  };
}

/// Loading state implementation
final class AsyncLoading<T> extends AsyncValue<T> {
  const AsyncLoading();

  @override
  bool operator ==(Object other) =>
      identical(this, other) || other is AsyncLoading<T>;

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() => 'AsyncLoading<$T>()';
}

/// Data state implementation
final class AsyncData<T> extends AsyncValue<T> {
  const AsyncData(this.data);

  @override
  final T data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AsyncData<T> &&
          runtimeType == other.runtimeType &&
          data == other.data;

  @override
  int get hashCode => data.hashCode;

  @override
  String toString() => 'AsyncData<$T>($data)';
}

/// Error state implementation
final class AsyncError<T> extends AsyncValue<T> {
  const AsyncError(this.error, [this.stackTrace]);

  @override
  final Object error;
  final StackTrace? stackTrace;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AsyncError<T> &&
          runtimeType == other.runtimeType &&
          error == other.error &&
          stackTrace == other.stackTrace;

  @override
  int get hashCode => Object.hash(error, stackTrace);

  @override
  String toString() => 'AsyncError<$T>($error, $stackTrace)';
}
