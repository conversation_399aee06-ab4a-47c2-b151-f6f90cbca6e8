import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';

/// Card widget showing profile completion status and missing fields
class ProfileCompletionCard extends StatelessWidget {
  final double completion;
  final List<String> missingFields;
  final VoidCallback? onComplete;

  const ProfileCompletionCard({
    super.key,
    required this.completion,
    required this.missingFields,
    this.onComplete,
  });

  @override
  Widget build(BuildContext context) {
    final completionPercentage = (completion * 100).round();
    final isComplete = completion >= 1.0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  isComplete ? Icons.check_circle : Icons.info_outline,
                  color: isComplete
                      ? AppColors.success
                      : Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    isComplete ? 'Profile Complete!' : 'Complete Your Profile',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: isComplete
                          ? AppColors.success
                          : Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ),
                Text(
                  '$completionPercentage%',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: isComplete
                        ? AppColors.success
                        : Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Progress Bar
            LinearProgressIndicator(
              value: completion,
              backgroundColor: Theme.of(
                context,
              ).colorScheme.surfaceContainerHighest,
              valueColor: AlwaysStoppedAnimation<Color>(
                isComplete
                    ? AppColors.success
                    : Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),

            // Description
            Text(
              isComplete
                  ? 'Your profile is complete and ready to use!'
                  : 'Complete your profile to get the best experience and access all features.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),

            // Missing Fields (if any)
            if (!isComplete && missingFields.isNotEmpty) ...[
              const SizedBox(height: 16),
              Text(
                'Missing Information:',
                style: Theme.of(
                  context,
                ).textTheme.labelLarge?.copyWith(fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 8),
              ...missingFields
                  .take(3)
                  .map(
                    (field) => Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Row(
                        children: [
                          Icon(
                            Icons.circle,
                            size: 6,
                            color: Theme.of(
                              context,
                            ).colorScheme.onSurfaceVariant,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            field,
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(
                                  color: Theme.of(
                                    context,
                                  ).colorScheme.onSurfaceVariant,
                                ),
                          ),
                        ],
                      ),
                    ),
                  ),
              if (missingFields.length > 3)
                Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: Text(
                    'and ${missingFields.length - 3} more...',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ),
            ],

            // Action Button
            if (!isComplete && onComplete != null) ...[
              const SizedBox(height: 20),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: onComplete,
                  child: const Text('Complete Profile'),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
