import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:auto_route/auto_route.dart';
import '../../domain/entities/user.dart';
import '../../domain/entities/rider_profile.dart';
import '../providers/user_profile_notifier.dart';
import '../providers/rider_profile_notifier.dart';
import '../widgets/error_display_widget.dart';

/// Screen for completing rider profile setup
@RoutePage()
class ProfileSetupScreen extends ConsumerStatefulWidget {
  final UserType userType;

  const ProfileSetupScreen({super.key, required this.userType});

  @override
  ConsumerState<ProfileSetupScreen> createState() => _ProfileSetupScreenState();
}

class _ProfileSetupScreenState extends ConsumerState<ProfileSetupScreen> {
  final _formKey = GlobalKey<FormState>();

  // Rider form controllers
  final _emergencyNameController = TextEditingController();
  final _emergencyPhoneController = TextEditingController();
  final _preferredLanguageController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadExistingData();
  }

  @override
  void dispose() {
    _disposeControllers();
    super.dispose();
  }

  void _disposeControllers() {
    _emergencyNameController.dispose();
    _emergencyPhoneController.dispose();
    _preferredLanguageController.dispose();
  }

  void _loadExistingData() {
    final riderProfile = ref.read(currentRiderProfileProvider);
    if (riderProfile != null) {
      _emergencyNameController.text = riderProfile.emergencyContactName ?? '';
      _emergencyPhoneController.text = riderProfile.emergencyContactPhone ?? '';
      _preferredLanguageController.text = riderProfile.preferredLanguage ?? '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Complete Rider Profile')),
      body: _buildRiderSetup(),
    );
  }

  Widget _buildRiderSetup() {
    final isLoading = ref.watch(isRiderProfileLoadingProvider);
    final error = ref.watch(riderProfileErrorProvider);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (error != null)
              ErrorDisplayWidget(
                message: error,
                onRetry: () => ref
                    .read(riderProfileNotifierProvider.notifier)
                    .clearError(),
              ),

            _buildSectionHeader('Emergency Contact Information'),
            const SizedBox(height: 16),

            _buildEmergencyContactFields(),

            const SizedBox(height: 24),
            _buildSectionHeader('Preferences'),
            const SizedBox(height: 16),

            _buildPreferredLanguageField(),

            const SizedBox(height: 32),

            if (isLoading)
              const Center(child: CircularProgressIndicator())
            else
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _saveRiderProfile,
                  child: const Text('Complete Profile'),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: Theme.of(
        context,
      ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
    );
  }

  Widget _buildEmergencyContactFields() {
    return Column(
      children: [
        TextFormField(
          controller: _emergencyNameController,
          decoration: const InputDecoration(
            labelText: 'Emergency Contact Name',
            hintText: 'Enter full name',
            prefixIcon: Icon(Icons.person),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Emergency contact name is required';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _emergencyPhoneController,
          decoration: const InputDecoration(
            labelText: 'Emergency Contact Phone',
            hintText: 'Enter phone number',
            prefixIcon: Icon(Icons.phone),
          ),
          keyboardType: TextInputType.phone,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Emergency contact phone is required';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildPreferredLanguageField() {
    return DropdownButtonFormField<String>(
      value: _preferredLanguageController.text.isEmpty
          ? null
          : _preferredLanguageController.text,
      decoration: const InputDecoration(
        labelText: 'Preferred Language',
        prefixIcon: Icon(Icons.language),
      ),
      items: const [
        DropdownMenuItem(value: 'en', child: Text('English')),
        DropdownMenuItem(value: 'fr', child: Text('French')),
        DropdownMenuItem(value: 'es', child: Text('Spanish')),
      ],
      onChanged: (value) {
        if (value != null) {
          _preferredLanguageController.text = value;
        }
      },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please select your preferred language';
        }
        return null;
      },
    );
  }

  void _saveRiderProfile() {
    if (!_formKey.currentState!.validate()) return;

    final currentProfile = ref.read(currentRiderProfileProvider);
    final user = ref.read(currentUserProfileProvider);

    if (user == null) return;

    final profile = RiderProfile(
      id: currentProfile?.id ?? '',
      userId: user.id,
      emergencyContactName: _emergencyNameController.text.trim(),
      emergencyContactPhone: _emergencyPhoneController.text.trim(),
      preferredLanguage: _preferredLanguageController.text,
      frequentDestinations: currentProfile?.frequentDestinations ?? [],
      isProfileComplete: true,
      createdAt: currentProfile?.createdAt ?? DateTime.now(),
      updatedAt: DateTime.now(),
    );

    if (currentProfile == null) {
      ref.read(riderProfileNotifierProvider.notifier).createProfile(profile);
    } else {
      ref.read(riderProfileNotifierProvider.notifier).updateProfile(profile);
    }

    context.router.maybePop();
  }
}
