import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../core/constants/app_constants.dart';
import '../../core/navigation/app_router.dart';
import '../providers/auth_notifier.dart';
import '../providers/auth_state.dart';

/// Splash screen that handles app initialization and authentication state
@RoutePage()
class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen> {
  @override
  void initState() {
    super.initState();
    // Initialize authentication state when splash screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeApp();
    });
  }

  /// Initialize the app and handle authentication state
  Future<void> _initializeApp() async {
    try {
      // Add minimum splash screen display time for better UX
      final initializationFuture = ref
          .read(authNotifierProvider.notifier)
          .initialize();
      final minimumDisplayTime = Future.delayed(const Duration(seconds: 2));

      // Wait for both initialization and minimum display time
      await Future.wait([initializationFuture, minimumDisplayTime]);

      // Check auth state and navigate accordingly
      if (mounted) {
        final authState = ref.read(authStateProvider);
        authState.when(
          initial: () => _navigateToWelcome(),
          loading: () => {}, // Stay on splash screen
          authenticated: (user) => _navigateToDashboard(),
          unauthenticated: () => _navigateToWelcome(),
          error: (message, errorCode) => _handleError(message),
        );
      }
    } catch (e) {
      if (mounted) {
        _handleError('Failed to initialize app: $e');
      }
    }
  }

  /// Navigate to welcome screen for unauthenticated users
  void _navigateToWelcome() {
    context.router.pushAndPopUntil(
      const WelcomeRoute(),
      predicate: (_) => false,
    );
  }

  /// Navigate to dashboard for authenticated users
  void _navigateToDashboard() {
    context.router.pushAndPopUntil(
      const DashboardRoute(),
      predicate: (_) => false,
    );
  }

  /// Handle initialization errors
  void _handleError(String message) {
    // Show error dialog and allow retry
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Initialization Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _initializeApp(); // Retry initialization
            },
            child: const Text('Retry'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToWelcome(); // Go to welcome screen
            },
            child: const Text('Continue'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Listen to auth state changes for real-time navigation
    ref.listen<AuthState>(authStateProvider, (previous, next) {
      if (mounted) {
        next.when(
          initial: () => {},
          loading: () => {},
          authenticated: (user) => _navigateToDashboard(),
          unauthenticated: () => _navigateToWelcome(),
          error: (message, errorCode) => _handleError(message),
        );
      }
    });

    return Scaffold(
      backgroundColor: Theme.of(context).primaryColor,
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // App logo
            Icon(Icons.local_taxi, size: 80, color: Colors.white),
            SizedBox(height: 24),
            Text(
              AppConstants.appName,
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            SizedBox(height: 48),
            // Loading indicator
            SizedBox(
              width: 32,
              height: 32,
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                strokeWidth: 3,
              ),
            ),
            SizedBox(height: 16),
            Text(
              'Initializing...',
              style: TextStyle(color: Colors.white70, fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }
}
