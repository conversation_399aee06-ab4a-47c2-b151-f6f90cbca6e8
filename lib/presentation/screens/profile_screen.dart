import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:auto_route/auto_route.dart';
import '../../domain/entities/user.dart';
import '../../core/navigation/app_router.dart';
import '../providers/user_profile_notifier.dart';
import '../providers/rider_profile_notifier.dart';

import '../widgets/profile_image_widget.dart';
import '../widgets/profile_completion_card.dart';
import '../widgets/profile_info_card.dart';
import '../widgets/profile_actions_card.dart';

/// Main profile screen for viewing and editing user information
@RoutePage()
class ProfileScreen extends ConsumerStatefulWidget {
  const ProfileScreen({super.key});

  @override
  ConsumerState<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends ConsumerState<ProfileScreen> {
  @override
  void initState() {
    super.initState();
    // Load profile data when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadProfileData();
    });
  }

  /// Load all profile data for rider
  void _loadProfileData() {
    ref.read(userProfileNotifierProvider.notifier).loadProfile();
    ref.read(riderProfileNotifierProvider.notifier).loadProfile();
  }

  /// Refresh all profile data
  Future<void> _refreshProfile() async {
    await ref.read(userProfileNotifierProvider.notifier).refreshProfile();
    await ref.read(riderProfileNotifierProvider.notifier).refreshProfile();
  }

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(currentUserProfileProvider);
    final isLoading = ref.watch(isUserProfileLoadingProvider);
    final error = ref.watch(userProfileErrorProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Profile'),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: user != null ? () => _navigateToEditProfile(user) : null,
            tooltip: 'Edit Profile',
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _refreshProfile,
        child: _buildBody(context, user, isLoading, error),
      ),
    );
  }

  /// Build the main body content
  Widget _buildBody(
    BuildContext context,
    User? user,
    bool isLoading,
    String? error,
  ) {
    if (error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text('Error', style: Theme.of(context).textTheme.headlineSmall),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(error, textAlign: TextAlign.center),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadProfileData,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (isLoading || user == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Profile Image and Basic Info
          _buildProfileHeader(user),
          const SizedBox(height: 24),

          // Profile Completion Card (if applicable)
          _buildProfileCompletionSection(user),

          // Profile Information Cards
          _buildProfileInfoSection(user),

          // Profile Actions
          _buildProfileActionsSection(user),

          const SizedBox(height: 32),
        ],
      ),
    );
  }

  /// Build profile header with image and basic info
  Widget _buildProfileHeader(User user) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // Profile Image
            ProfileImageWidget(
              imageUrl: user.profileImage,
              size: 100,
              onTap: () => _showImageOptions(context),
            ),
            const SizedBox(height: 16),

            // User Name
            Text(
              user.name,
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),

            // User Type Badge
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                user.userType.name,
                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            const SizedBox(height: 8),

            // Email
            Text(
              user.email,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),

            // Phone (if available)
            if (user.phone != null) ...[
              const SizedBox(height: 4),
              Text(
                user.phone!,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Build profile completion section for rider
  Widget _buildProfileCompletionSection(User user) {
    final riderProfile = ref.watch(currentRiderProfileProvider);
    final completion = ref.watch(riderProfileCompletionProvider);
    final missingFields = ref.watch(riderProfileMissingFieldsProvider);

    if (riderProfile != null && !riderProfile.isProfileComplete) {
      return Column(
        children: [
          ProfileCompletionCard(
            completion: completion,
            missingFields: missingFields,
            onComplete: () => _navigateToProfileSetup(user),
          ),
          const SizedBox(height: 16),
        ],
      );
    }

    return const SizedBox.shrink();
  }

  /// Build profile information section
  Widget _buildProfileInfoSection(User user) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Profile Information',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),

        _buildRiderProfileInfo(),
      ],
    );
  }

  /// Build rider-specific profile information
  Widget _buildRiderProfileInfo() {
    final riderProfile = ref.watch(currentRiderProfileProvider);
    final isLoading = ref.watch(isRiderProfileLoadingProvider);

    if (isLoading) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(20),
          child: Center(child: CircularProgressIndicator()),
        ),
      );
    }

    return ProfileInfoCard(
      title: 'Rider Information',
      items: [
        if (riderProfile?.emergencyContactName != null)
          ProfileInfoItem(
            label: 'Emergency Contact',
            value: riderProfile!.emergencyContactName!,
            icon: Icons.contact_emergency,
          ),
        if (riderProfile?.emergencyContactPhone != null)
          ProfileInfoItem(
            label: 'Emergency Phone',
            value: riderProfile!.emergencyContactPhone!,
            icon: Icons.phone,
          ),
        if (riderProfile?.preferredLanguage != null)
          ProfileInfoItem(
            label: 'Preferred Language',
            value: riderProfile!.preferredLanguage!,
            icon: Icons.language,
          ),
        if (riderProfile?.frequentDestinations.isNotEmpty == true)
          ProfileInfoItem(
            label: 'Frequent Destinations',
            value: '${riderProfile!.frequentDestinations.length} saved',
            icon: Icons.location_on,
          ),
      ],
    );
  }

  /// Build profile actions section
  Widget _buildProfileActionsSection(User user) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 24),
        Text(
          'Actions',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),

        ProfileActionsCard(
          actions: [
            ProfileAction(
              title: 'Edit Profile',
              subtitle: 'Update your personal information',
              icon: Icons.edit,
              onTap: () => _navigateToEditProfile(user),
            ),
            ProfileAction(
              title: 'Complete Profile Setup',
              subtitle: 'Finish setting up your rider profile',
              icon: Icons.assignment_turned_in,
              onTap: () => _navigateToProfileSetup(user),
            ),
            ProfileAction(
              title: 'Settings',
              subtitle: 'App preferences and account settings',
              icon: Icons.settings,
              onTap: () => _navigateToSettings(),
            ),
          ],
        ),
      ],
    );
  }

  /// Show profile image options
  void _showImageOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('Take Photo'),
              onTap: () {
                Navigator.pop(context);
                _selectImageFromCamera();
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('Choose from Gallery'),
              onTap: () {
                Navigator.pop(context);
                _selectImageFromGallery();
              },
            ),
            if (ref.read(currentUserProfileProvider)?.profileImage != null)
              ListTile(
                leading: const Icon(Icons.delete),
                title: const Text('Remove Photo'),
                onTap: () {
                  Navigator.pop(context);
                  _removeProfileImage();
                },
              ),
          ],
        ),
      ),
    );
  }

  /// Select image from camera
  void _selectImageFromCamera() {
    // TODO: Implement camera image selection
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Camera functionality not implemented yet')),
    );
  }

  /// Select image from gallery
  void _selectImageFromGallery() {
    // TODO: Implement gallery image selection
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Gallery functionality not implemented yet'),
      ),
    );
  }

  /// Remove profile image
  void _removeProfileImage() {
    ref.read(profileImageNotifierProvider.notifier).deleteImage();
  }

  /// Navigate to edit profile screen
  void _navigateToEditProfile(User user) async {
    // Navigate to edit profile and wait for result
    await context.router.push(EditProfileRoute(user: user));
    // Refresh profile data when returning from edit screen
    _refreshProfile();
  }

  /// Navigate to profile setup screen
  void _navigateToProfileSetup(User user) {
    context.router.push(ProfileSetupRoute(userType: user.userType));
  }

  /// Navigate to settings screen
  void _navigateToSettings() {
    // TODO: Navigate to settings screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Settings screen not implemented yet')),
    );
  }
}
