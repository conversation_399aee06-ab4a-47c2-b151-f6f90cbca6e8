// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'profile_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$UserProfileState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(User user) loaded,
    required TResult Function(String message, String? errorCode) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(User user)? loaded,
    TResult? Function(String message, String? errorCode)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(User user)? loaded,
    TResult Function(String message, String? errorCode)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UserProfileInitial value) initial,
    required TResult Function(UserProfileLoading value) loading,
    required TResult Function(UserProfileLoaded value) loaded,
    required TResult Function(UserProfileError value) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UserProfileInitial value)? initial,
    TResult? Function(UserProfileLoading value)? loading,
    TResult? Function(UserProfileLoaded value)? loaded,
    TResult? Function(UserProfileError value)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UserProfileInitial value)? initial,
    TResult Function(UserProfileLoading value)? loading,
    TResult Function(UserProfileLoaded value)? loaded,
    TResult Function(UserProfileError value)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserProfileStateCopyWith<$Res> {
  factory $UserProfileStateCopyWith(
          UserProfileState value, $Res Function(UserProfileState) then) =
      _$UserProfileStateCopyWithImpl<$Res, UserProfileState>;
}

/// @nodoc
class _$UserProfileStateCopyWithImpl<$Res, $Val extends UserProfileState>
    implements $UserProfileStateCopyWith<$Res> {
  _$UserProfileStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserProfileState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$UserProfileInitialImplCopyWith<$Res> {
  factory _$$UserProfileInitialImplCopyWith(_$UserProfileInitialImpl value,
          $Res Function(_$UserProfileInitialImpl) then) =
      __$$UserProfileInitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UserProfileInitialImplCopyWithImpl<$Res>
    extends _$UserProfileStateCopyWithImpl<$Res, _$UserProfileInitialImpl>
    implements _$$UserProfileInitialImplCopyWith<$Res> {
  __$$UserProfileInitialImplCopyWithImpl(_$UserProfileInitialImpl _value,
      $Res Function(_$UserProfileInitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserProfileState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$UserProfileInitialImpl implements UserProfileInitial {
  const _$UserProfileInitialImpl();

  @override
  String toString() {
    return 'UserProfileState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$UserProfileInitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(User user) loaded,
    required TResult Function(String message, String? errorCode) error,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(User user)? loaded,
    TResult? Function(String message, String? errorCode)? error,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(User user)? loaded,
    TResult Function(String message, String? errorCode)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UserProfileInitial value) initial,
    required TResult Function(UserProfileLoading value) loading,
    required TResult Function(UserProfileLoaded value) loaded,
    required TResult Function(UserProfileError value) error,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UserProfileInitial value)? initial,
    TResult? Function(UserProfileLoading value)? loading,
    TResult? Function(UserProfileLoaded value)? loaded,
    TResult? Function(UserProfileError value)? error,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UserProfileInitial value)? initial,
    TResult Function(UserProfileLoading value)? loading,
    TResult Function(UserProfileLoaded value)? loaded,
    TResult Function(UserProfileError value)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class UserProfileInitial implements UserProfileState {
  const factory UserProfileInitial() = _$UserProfileInitialImpl;
}

/// @nodoc
abstract class _$$UserProfileLoadingImplCopyWith<$Res> {
  factory _$$UserProfileLoadingImplCopyWith(_$UserProfileLoadingImpl value,
          $Res Function(_$UserProfileLoadingImpl) then) =
      __$$UserProfileLoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UserProfileLoadingImplCopyWithImpl<$Res>
    extends _$UserProfileStateCopyWithImpl<$Res, _$UserProfileLoadingImpl>
    implements _$$UserProfileLoadingImplCopyWith<$Res> {
  __$$UserProfileLoadingImplCopyWithImpl(_$UserProfileLoadingImpl _value,
      $Res Function(_$UserProfileLoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserProfileState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$UserProfileLoadingImpl implements UserProfileLoading {
  const _$UserProfileLoadingImpl();

  @override
  String toString() {
    return 'UserProfileState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$UserProfileLoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(User user) loaded,
    required TResult Function(String message, String? errorCode) error,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(User user)? loaded,
    TResult? Function(String message, String? errorCode)? error,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(User user)? loaded,
    TResult Function(String message, String? errorCode)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UserProfileInitial value) initial,
    required TResult Function(UserProfileLoading value) loading,
    required TResult Function(UserProfileLoaded value) loaded,
    required TResult Function(UserProfileError value) error,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UserProfileInitial value)? initial,
    TResult? Function(UserProfileLoading value)? loading,
    TResult? Function(UserProfileLoaded value)? loaded,
    TResult? Function(UserProfileError value)? error,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UserProfileInitial value)? initial,
    TResult Function(UserProfileLoading value)? loading,
    TResult Function(UserProfileLoaded value)? loaded,
    TResult Function(UserProfileError value)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class UserProfileLoading implements UserProfileState {
  const factory UserProfileLoading() = _$UserProfileLoadingImpl;
}

/// @nodoc
abstract class _$$UserProfileLoadedImplCopyWith<$Res> {
  factory _$$UserProfileLoadedImplCopyWith(_$UserProfileLoadedImpl value,
          $Res Function(_$UserProfileLoadedImpl) then) =
      __$$UserProfileLoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({User user});

  $UserCopyWith<$Res> get user;
}

/// @nodoc
class __$$UserProfileLoadedImplCopyWithImpl<$Res>
    extends _$UserProfileStateCopyWithImpl<$Res, _$UserProfileLoadedImpl>
    implements _$$UserProfileLoadedImplCopyWith<$Res> {
  __$$UserProfileLoadedImplCopyWithImpl(_$UserProfileLoadedImpl _value,
      $Res Function(_$UserProfileLoadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserProfileState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? user = null,
  }) {
    return _then(_$UserProfileLoadedImpl(
      user: null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User,
    ));
  }

  /// Create a copy of UserProfileState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserCopyWith<$Res> get user {
    return $UserCopyWith<$Res>(_value.user, (value) {
      return _then(_value.copyWith(user: value));
    });
  }
}

/// @nodoc

class _$UserProfileLoadedImpl implements UserProfileLoaded {
  const _$UserProfileLoadedImpl({required this.user});

  @override
  final User user;

  @override
  String toString() {
    return 'UserProfileState.loaded(user: $user)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserProfileLoadedImpl &&
            (identical(other.user, user) || other.user == user));
  }

  @override
  int get hashCode => Object.hash(runtimeType, user);

  /// Create a copy of UserProfileState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserProfileLoadedImplCopyWith<_$UserProfileLoadedImpl> get copyWith =>
      __$$UserProfileLoadedImplCopyWithImpl<_$UserProfileLoadedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(User user) loaded,
    required TResult Function(String message, String? errorCode) error,
  }) {
    return loaded(user);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(User user)? loaded,
    TResult? Function(String message, String? errorCode)? error,
  }) {
    return loaded?.call(user);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(User user)? loaded,
    TResult Function(String message, String? errorCode)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(user);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UserProfileInitial value) initial,
    required TResult Function(UserProfileLoading value) loading,
    required TResult Function(UserProfileLoaded value) loaded,
    required TResult Function(UserProfileError value) error,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UserProfileInitial value)? initial,
    TResult? Function(UserProfileLoading value)? loading,
    TResult? Function(UserProfileLoaded value)? loaded,
    TResult? Function(UserProfileError value)? error,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UserProfileInitial value)? initial,
    TResult Function(UserProfileLoading value)? loading,
    TResult Function(UserProfileLoaded value)? loaded,
    TResult Function(UserProfileError value)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class UserProfileLoaded implements UserProfileState {
  const factory UserProfileLoaded({required final User user}) =
      _$UserProfileLoadedImpl;

  User get user;

  /// Create a copy of UserProfileState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserProfileLoadedImplCopyWith<_$UserProfileLoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UserProfileErrorImplCopyWith<$Res> {
  factory _$$UserProfileErrorImplCopyWith(_$UserProfileErrorImpl value,
          $Res Function(_$UserProfileErrorImpl) then) =
      __$$UserProfileErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message, String? errorCode});
}

/// @nodoc
class __$$UserProfileErrorImplCopyWithImpl<$Res>
    extends _$UserProfileStateCopyWithImpl<$Res, _$UserProfileErrorImpl>
    implements _$$UserProfileErrorImplCopyWith<$Res> {
  __$$UserProfileErrorImplCopyWithImpl(_$UserProfileErrorImpl _value,
      $Res Function(_$UserProfileErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserProfileState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? errorCode = freezed,
  }) {
    return _then(_$UserProfileErrorImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      errorCode: freezed == errorCode
          ? _value.errorCode
          : errorCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$UserProfileErrorImpl implements UserProfileError {
  const _$UserProfileErrorImpl({required this.message, this.errorCode});

  @override
  final String message;
  @override
  final String? errorCode;

  @override
  String toString() {
    return 'UserProfileState.error(message: $message, errorCode: $errorCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserProfileErrorImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.errorCode, errorCode) ||
                other.errorCode == errorCode));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, errorCode);

  /// Create a copy of UserProfileState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserProfileErrorImplCopyWith<_$UserProfileErrorImpl> get copyWith =>
      __$$UserProfileErrorImplCopyWithImpl<_$UserProfileErrorImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(User user) loaded,
    required TResult Function(String message, String? errorCode) error,
  }) {
    return error(message, errorCode);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(User user)? loaded,
    TResult? Function(String message, String? errorCode)? error,
  }) {
    return error?.call(message, errorCode);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(User user)? loaded,
    TResult Function(String message, String? errorCode)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message, errorCode);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UserProfileInitial value) initial,
    required TResult Function(UserProfileLoading value) loading,
    required TResult Function(UserProfileLoaded value) loaded,
    required TResult Function(UserProfileError value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UserProfileInitial value)? initial,
    TResult? Function(UserProfileLoading value)? loading,
    TResult? Function(UserProfileLoaded value)? loaded,
    TResult? Function(UserProfileError value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UserProfileInitial value)? initial,
    TResult Function(UserProfileLoading value)? loading,
    TResult Function(UserProfileLoaded value)? loaded,
    TResult Function(UserProfileError value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class UserProfileError implements UserProfileState {
  const factory UserProfileError(
      {required final String message,
      final String? errorCode}) = _$UserProfileErrorImpl;

  String get message;
  String? get errorCode;

  /// Create a copy of UserProfileState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserProfileErrorImplCopyWith<_$UserProfileErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$RiderProfileState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(RiderProfile? profile) loaded,
    required TResult Function(String message, String? errorCode) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(RiderProfile? profile)? loaded,
    TResult? Function(String message, String? errorCode)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(RiderProfile? profile)? loaded,
    TResult Function(String message, String? errorCode)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(RiderProfileInitial value) initial,
    required TResult Function(RiderProfileLoading value) loading,
    required TResult Function(RiderProfileLoaded value) loaded,
    required TResult Function(RiderProfileError value) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(RiderProfileInitial value)? initial,
    TResult? Function(RiderProfileLoading value)? loading,
    TResult? Function(RiderProfileLoaded value)? loaded,
    TResult? Function(RiderProfileError value)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(RiderProfileInitial value)? initial,
    TResult Function(RiderProfileLoading value)? loading,
    TResult Function(RiderProfileLoaded value)? loaded,
    TResult Function(RiderProfileError value)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RiderProfileStateCopyWith<$Res> {
  factory $RiderProfileStateCopyWith(
          RiderProfileState value, $Res Function(RiderProfileState) then) =
      _$RiderProfileStateCopyWithImpl<$Res, RiderProfileState>;
}

/// @nodoc
class _$RiderProfileStateCopyWithImpl<$Res, $Val extends RiderProfileState>
    implements $RiderProfileStateCopyWith<$Res> {
  _$RiderProfileStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RiderProfileState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$RiderProfileInitialImplCopyWith<$Res> {
  factory _$$RiderProfileInitialImplCopyWith(_$RiderProfileInitialImpl value,
          $Res Function(_$RiderProfileInitialImpl) then) =
      __$$RiderProfileInitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$RiderProfileInitialImplCopyWithImpl<$Res>
    extends _$RiderProfileStateCopyWithImpl<$Res, _$RiderProfileInitialImpl>
    implements _$$RiderProfileInitialImplCopyWith<$Res> {
  __$$RiderProfileInitialImplCopyWithImpl(_$RiderProfileInitialImpl _value,
      $Res Function(_$RiderProfileInitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of RiderProfileState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$RiderProfileInitialImpl implements RiderProfileInitial {
  const _$RiderProfileInitialImpl();

  @override
  String toString() {
    return 'RiderProfileState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RiderProfileInitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(RiderProfile? profile) loaded,
    required TResult Function(String message, String? errorCode) error,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(RiderProfile? profile)? loaded,
    TResult? Function(String message, String? errorCode)? error,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(RiderProfile? profile)? loaded,
    TResult Function(String message, String? errorCode)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(RiderProfileInitial value) initial,
    required TResult Function(RiderProfileLoading value) loading,
    required TResult Function(RiderProfileLoaded value) loaded,
    required TResult Function(RiderProfileError value) error,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(RiderProfileInitial value)? initial,
    TResult? Function(RiderProfileLoading value)? loading,
    TResult? Function(RiderProfileLoaded value)? loaded,
    TResult? Function(RiderProfileError value)? error,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(RiderProfileInitial value)? initial,
    TResult Function(RiderProfileLoading value)? loading,
    TResult Function(RiderProfileLoaded value)? loaded,
    TResult Function(RiderProfileError value)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class RiderProfileInitial implements RiderProfileState {
  const factory RiderProfileInitial() = _$RiderProfileInitialImpl;
}

/// @nodoc
abstract class _$$RiderProfileLoadingImplCopyWith<$Res> {
  factory _$$RiderProfileLoadingImplCopyWith(_$RiderProfileLoadingImpl value,
          $Res Function(_$RiderProfileLoadingImpl) then) =
      __$$RiderProfileLoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$RiderProfileLoadingImplCopyWithImpl<$Res>
    extends _$RiderProfileStateCopyWithImpl<$Res, _$RiderProfileLoadingImpl>
    implements _$$RiderProfileLoadingImplCopyWith<$Res> {
  __$$RiderProfileLoadingImplCopyWithImpl(_$RiderProfileLoadingImpl _value,
      $Res Function(_$RiderProfileLoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of RiderProfileState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$RiderProfileLoadingImpl implements RiderProfileLoading {
  const _$RiderProfileLoadingImpl();

  @override
  String toString() {
    return 'RiderProfileState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RiderProfileLoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(RiderProfile? profile) loaded,
    required TResult Function(String message, String? errorCode) error,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(RiderProfile? profile)? loaded,
    TResult? Function(String message, String? errorCode)? error,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(RiderProfile? profile)? loaded,
    TResult Function(String message, String? errorCode)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(RiderProfileInitial value) initial,
    required TResult Function(RiderProfileLoading value) loading,
    required TResult Function(RiderProfileLoaded value) loaded,
    required TResult Function(RiderProfileError value) error,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(RiderProfileInitial value)? initial,
    TResult? Function(RiderProfileLoading value)? loading,
    TResult? Function(RiderProfileLoaded value)? loaded,
    TResult? Function(RiderProfileError value)? error,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(RiderProfileInitial value)? initial,
    TResult Function(RiderProfileLoading value)? loading,
    TResult Function(RiderProfileLoaded value)? loaded,
    TResult Function(RiderProfileError value)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class RiderProfileLoading implements RiderProfileState {
  const factory RiderProfileLoading() = _$RiderProfileLoadingImpl;
}

/// @nodoc
abstract class _$$RiderProfileLoadedImplCopyWith<$Res> {
  factory _$$RiderProfileLoadedImplCopyWith(_$RiderProfileLoadedImpl value,
          $Res Function(_$RiderProfileLoadedImpl) then) =
      __$$RiderProfileLoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({RiderProfile? profile});

  $RiderProfileCopyWith<$Res>? get profile;
}

/// @nodoc
class __$$RiderProfileLoadedImplCopyWithImpl<$Res>
    extends _$RiderProfileStateCopyWithImpl<$Res, _$RiderProfileLoadedImpl>
    implements _$$RiderProfileLoadedImplCopyWith<$Res> {
  __$$RiderProfileLoadedImplCopyWithImpl(_$RiderProfileLoadedImpl _value,
      $Res Function(_$RiderProfileLoadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of RiderProfileState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? profile = freezed,
  }) {
    return _then(_$RiderProfileLoadedImpl(
      profile: freezed == profile
          ? _value.profile
          : profile // ignore: cast_nullable_to_non_nullable
              as RiderProfile?,
    ));
  }

  /// Create a copy of RiderProfileState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RiderProfileCopyWith<$Res>? get profile {
    if (_value.profile == null) {
      return null;
    }

    return $RiderProfileCopyWith<$Res>(_value.profile!, (value) {
      return _then(_value.copyWith(profile: value));
    });
  }
}

/// @nodoc

class _$RiderProfileLoadedImpl implements RiderProfileLoaded {
  const _$RiderProfileLoadedImpl({this.profile});

  @override
  final RiderProfile? profile;

  @override
  String toString() {
    return 'RiderProfileState.loaded(profile: $profile)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RiderProfileLoadedImpl &&
            (identical(other.profile, profile) || other.profile == profile));
  }

  @override
  int get hashCode => Object.hash(runtimeType, profile);

  /// Create a copy of RiderProfileState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RiderProfileLoadedImplCopyWith<_$RiderProfileLoadedImpl> get copyWith =>
      __$$RiderProfileLoadedImplCopyWithImpl<_$RiderProfileLoadedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(RiderProfile? profile) loaded,
    required TResult Function(String message, String? errorCode) error,
  }) {
    return loaded(profile);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(RiderProfile? profile)? loaded,
    TResult? Function(String message, String? errorCode)? error,
  }) {
    return loaded?.call(profile);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(RiderProfile? profile)? loaded,
    TResult Function(String message, String? errorCode)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(profile);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(RiderProfileInitial value) initial,
    required TResult Function(RiderProfileLoading value) loading,
    required TResult Function(RiderProfileLoaded value) loaded,
    required TResult Function(RiderProfileError value) error,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(RiderProfileInitial value)? initial,
    TResult? Function(RiderProfileLoading value)? loading,
    TResult? Function(RiderProfileLoaded value)? loaded,
    TResult? Function(RiderProfileError value)? error,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(RiderProfileInitial value)? initial,
    TResult Function(RiderProfileLoading value)? loading,
    TResult Function(RiderProfileLoaded value)? loaded,
    TResult Function(RiderProfileError value)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class RiderProfileLoaded implements RiderProfileState {
  const factory RiderProfileLoaded({final RiderProfile? profile}) =
      _$RiderProfileLoadedImpl;

  RiderProfile? get profile;

  /// Create a copy of RiderProfileState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RiderProfileLoadedImplCopyWith<_$RiderProfileLoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RiderProfileErrorImplCopyWith<$Res> {
  factory _$$RiderProfileErrorImplCopyWith(_$RiderProfileErrorImpl value,
          $Res Function(_$RiderProfileErrorImpl) then) =
      __$$RiderProfileErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message, String? errorCode});
}

/// @nodoc
class __$$RiderProfileErrorImplCopyWithImpl<$Res>
    extends _$RiderProfileStateCopyWithImpl<$Res, _$RiderProfileErrorImpl>
    implements _$$RiderProfileErrorImplCopyWith<$Res> {
  __$$RiderProfileErrorImplCopyWithImpl(_$RiderProfileErrorImpl _value,
      $Res Function(_$RiderProfileErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of RiderProfileState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? errorCode = freezed,
  }) {
    return _then(_$RiderProfileErrorImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      errorCode: freezed == errorCode
          ? _value.errorCode
          : errorCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$RiderProfileErrorImpl implements RiderProfileError {
  const _$RiderProfileErrorImpl({required this.message, this.errorCode});

  @override
  final String message;
  @override
  final String? errorCode;

  @override
  String toString() {
    return 'RiderProfileState.error(message: $message, errorCode: $errorCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RiderProfileErrorImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.errorCode, errorCode) ||
                other.errorCode == errorCode));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, errorCode);

  /// Create a copy of RiderProfileState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RiderProfileErrorImplCopyWith<_$RiderProfileErrorImpl> get copyWith =>
      __$$RiderProfileErrorImplCopyWithImpl<_$RiderProfileErrorImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(RiderProfile? profile) loaded,
    required TResult Function(String message, String? errorCode) error,
  }) {
    return error(message, errorCode);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(RiderProfile? profile)? loaded,
    TResult? Function(String message, String? errorCode)? error,
  }) {
    return error?.call(message, errorCode);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(RiderProfile? profile)? loaded,
    TResult Function(String message, String? errorCode)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message, errorCode);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(RiderProfileInitial value) initial,
    required TResult Function(RiderProfileLoading value) loading,
    required TResult Function(RiderProfileLoaded value) loaded,
    required TResult Function(RiderProfileError value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(RiderProfileInitial value)? initial,
    TResult? Function(RiderProfileLoading value)? loading,
    TResult? Function(RiderProfileLoaded value)? loaded,
    TResult? Function(RiderProfileError value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(RiderProfileInitial value)? initial,
    TResult Function(RiderProfileLoading value)? loading,
    TResult Function(RiderProfileLoaded value)? loaded,
    TResult Function(RiderProfileError value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class RiderProfileError implements RiderProfileState {
  const factory RiderProfileError(
      {required final String message,
      final String? errorCode}) = _$RiderProfileErrorImpl;

  String get message;
  String? get errorCode;

  /// Create a copy of RiderProfileState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RiderProfileErrorImplCopyWith<_$RiderProfileErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$ProfileImageState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() uploading,
    required TResult Function(String imageUrl) uploaded,
    required TResult Function(String message, String? errorCode) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? uploading,
    TResult? Function(String imageUrl)? uploaded,
    TResult? Function(String message, String? errorCode)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? uploading,
    TResult Function(String imageUrl)? uploaded,
    TResult Function(String message, String? errorCode)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ProfileImageInitial value) initial,
    required TResult Function(ProfileImageUploading value) uploading,
    required TResult Function(ProfileImageUploaded value) uploaded,
    required TResult Function(ProfileImageError value) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ProfileImageInitial value)? initial,
    TResult? Function(ProfileImageUploading value)? uploading,
    TResult? Function(ProfileImageUploaded value)? uploaded,
    TResult? Function(ProfileImageError value)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ProfileImageInitial value)? initial,
    TResult Function(ProfileImageUploading value)? uploading,
    TResult Function(ProfileImageUploaded value)? uploaded,
    TResult Function(ProfileImageError value)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProfileImageStateCopyWith<$Res> {
  factory $ProfileImageStateCopyWith(
          ProfileImageState value, $Res Function(ProfileImageState) then) =
      _$ProfileImageStateCopyWithImpl<$Res, ProfileImageState>;
}

/// @nodoc
class _$ProfileImageStateCopyWithImpl<$Res, $Val extends ProfileImageState>
    implements $ProfileImageStateCopyWith<$Res> {
  _$ProfileImageStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProfileImageState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$ProfileImageInitialImplCopyWith<$Res> {
  factory _$$ProfileImageInitialImplCopyWith(_$ProfileImageInitialImpl value,
          $Res Function(_$ProfileImageInitialImpl) then) =
      __$$ProfileImageInitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ProfileImageInitialImplCopyWithImpl<$Res>
    extends _$ProfileImageStateCopyWithImpl<$Res, _$ProfileImageInitialImpl>
    implements _$$ProfileImageInitialImplCopyWith<$Res> {
  __$$ProfileImageInitialImplCopyWithImpl(_$ProfileImageInitialImpl _value,
      $Res Function(_$ProfileImageInitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProfileImageState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ProfileImageInitialImpl implements ProfileImageInitial {
  const _$ProfileImageInitialImpl();

  @override
  String toString() {
    return 'ProfileImageState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProfileImageInitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() uploading,
    required TResult Function(String imageUrl) uploaded,
    required TResult Function(String message, String? errorCode) error,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? uploading,
    TResult? Function(String imageUrl)? uploaded,
    TResult? Function(String message, String? errorCode)? error,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? uploading,
    TResult Function(String imageUrl)? uploaded,
    TResult Function(String message, String? errorCode)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ProfileImageInitial value) initial,
    required TResult Function(ProfileImageUploading value) uploading,
    required TResult Function(ProfileImageUploaded value) uploaded,
    required TResult Function(ProfileImageError value) error,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ProfileImageInitial value)? initial,
    TResult? Function(ProfileImageUploading value)? uploading,
    TResult? Function(ProfileImageUploaded value)? uploaded,
    TResult? Function(ProfileImageError value)? error,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ProfileImageInitial value)? initial,
    TResult Function(ProfileImageUploading value)? uploading,
    TResult Function(ProfileImageUploaded value)? uploaded,
    TResult Function(ProfileImageError value)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class ProfileImageInitial implements ProfileImageState {
  const factory ProfileImageInitial() = _$ProfileImageInitialImpl;
}

/// @nodoc
abstract class _$$ProfileImageUploadingImplCopyWith<$Res> {
  factory _$$ProfileImageUploadingImplCopyWith(
          _$ProfileImageUploadingImpl value,
          $Res Function(_$ProfileImageUploadingImpl) then) =
      __$$ProfileImageUploadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ProfileImageUploadingImplCopyWithImpl<$Res>
    extends _$ProfileImageStateCopyWithImpl<$Res, _$ProfileImageUploadingImpl>
    implements _$$ProfileImageUploadingImplCopyWith<$Res> {
  __$$ProfileImageUploadingImplCopyWithImpl(_$ProfileImageUploadingImpl _value,
      $Res Function(_$ProfileImageUploadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProfileImageState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ProfileImageUploadingImpl implements ProfileImageUploading {
  const _$ProfileImageUploadingImpl();

  @override
  String toString() {
    return 'ProfileImageState.uploading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProfileImageUploadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() uploading,
    required TResult Function(String imageUrl) uploaded,
    required TResult Function(String message, String? errorCode) error,
  }) {
    return uploading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? uploading,
    TResult? Function(String imageUrl)? uploaded,
    TResult? Function(String message, String? errorCode)? error,
  }) {
    return uploading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? uploading,
    TResult Function(String imageUrl)? uploaded,
    TResult Function(String message, String? errorCode)? error,
    required TResult orElse(),
  }) {
    if (uploading != null) {
      return uploading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ProfileImageInitial value) initial,
    required TResult Function(ProfileImageUploading value) uploading,
    required TResult Function(ProfileImageUploaded value) uploaded,
    required TResult Function(ProfileImageError value) error,
  }) {
    return uploading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ProfileImageInitial value)? initial,
    TResult? Function(ProfileImageUploading value)? uploading,
    TResult? Function(ProfileImageUploaded value)? uploaded,
    TResult? Function(ProfileImageError value)? error,
  }) {
    return uploading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ProfileImageInitial value)? initial,
    TResult Function(ProfileImageUploading value)? uploading,
    TResult Function(ProfileImageUploaded value)? uploaded,
    TResult Function(ProfileImageError value)? error,
    required TResult orElse(),
  }) {
    if (uploading != null) {
      return uploading(this);
    }
    return orElse();
  }
}

abstract class ProfileImageUploading implements ProfileImageState {
  const factory ProfileImageUploading() = _$ProfileImageUploadingImpl;
}

/// @nodoc
abstract class _$$ProfileImageUploadedImplCopyWith<$Res> {
  factory _$$ProfileImageUploadedImplCopyWith(_$ProfileImageUploadedImpl value,
          $Res Function(_$ProfileImageUploadedImpl) then) =
      __$$ProfileImageUploadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String imageUrl});
}

/// @nodoc
class __$$ProfileImageUploadedImplCopyWithImpl<$Res>
    extends _$ProfileImageStateCopyWithImpl<$Res, _$ProfileImageUploadedImpl>
    implements _$$ProfileImageUploadedImplCopyWith<$Res> {
  __$$ProfileImageUploadedImplCopyWithImpl(_$ProfileImageUploadedImpl _value,
      $Res Function(_$ProfileImageUploadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProfileImageState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? imageUrl = null,
  }) {
    return _then(_$ProfileImageUploadedImpl(
      imageUrl: null == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ProfileImageUploadedImpl implements ProfileImageUploaded {
  const _$ProfileImageUploadedImpl({required this.imageUrl});

  @override
  final String imageUrl;

  @override
  String toString() {
    return 'ProfileImageState.uploaded(imageUrl: $imageUrl)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProfileImageUploadedImpl &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl));
  }

  @override
  int get hashCode => Object.hash(runtimeType, imageUrl);

  /// Create a copy of ProfileImageState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProfileImageUploadedImplCopyWith<_$ProfileImageUploadedImpl>
      get copyWith =>
          __$$ProfileImageUploadedImplCopyWithImpl<_$ProfileImageUploadedImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() uploading,
    required TResult Function(String imageUrl) uploaded,
    required TResult Function(String message, String? errorCode) error,
  }) {
    return uploaded(imageUrl);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? uploading,
    TResult? Function(String imageUrl)? uploaded,
    TResult? Function(String message, String? errorCode)? error,
  }) {
    return uploaded?.call(imageUrl);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? uploading,
    TResult Function(String imageUrl)? uploaded,
    TResult Function(String message, String? errorCode)? error,
    required TResult orElse(),
  }) {
    if (uploaded != null) {
      return uploaded(imageUrl);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ProfileImageInitial value) initial,
    required TResult Function(ProfileImageUploading value) uploading,
    required TResult Function(ProfileImageUploaded value) uploaded,
    required TResult Function(ProfileImageError value) error,
  }) {
    return uploaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ProfileImageInitial value)? initial,
    TResult? Function(ProfileImageUploading value)? uploading,
    TResult? Function(ProfileImageUploaded value)? uploaded,
    TResult? Function(ProfileImageError value)? error,
  }) {
    return uploaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ProfileImageInitial value)? initial,
    TResult Function(ProfileImageUploading value)? uploading,
    TResult Function(ProfileImageUploaded value)? uploaded,
    TResult Function(ProfileImageError value)? error,
    required TResult orElse(),
  }) {
    if (uploaded != null) {
      return uploaded(this);
    }
    return orElse();
  }
}

abstract class ProfileImageUploaded implements ProfileImageState {
  const factory ProfileImageUploaded({required final String imageUrl}) =
      _$ProfileImageUploadedImpl;

  String get imageUrl;

  /// Create a copy of ProfileImageState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProfileImageUploadedImplCopyWith<_$ProfileImageUploadedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ProfileImageErrorImplCopyWith<$Res> {
  factory _$$ProfileImageErrorImplCopyWith(_$ProfileImageErrorImpl value,
          $Res Function(_$ProfileImageErrorImpl) then) =
      __$$ProfileImageErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message, String? errorCode});
}

/// @nodoc
class __$$ProfileImageErrorImplCopyWithImpl<$Res>
    extends _$ProfileImageStateCopyWithImpl<$Res, _$ProfileImageErrorImpl>
    implements _$$ProfileImageErrorImplCopyWith<$Res> {
  __$$ProfileImageErrorImplCopyWithImpl(_$ProfileImageErrorImpl _value,
      $Res Function(_$ProfileImageErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProfileImageState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? errorCode = freezed,
  }) {
    return _then(_$ProfileImageErrorImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      errorCode: freezed == errorCode
          ? _value.errorCode
          : errorCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$ProfileImageErrorImpl implements ProfileImageError {
  const _$ProfileImageErrorImpl({required this.message, this.errorCode});

  @override
  final String message;
  @override
  final String? errorCode;

  @override
  String toString() {
    return 'ProfileImageState.error(message: $message, errorCode: $errorCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProfileImageErrorImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.errorCode, errorCode) ||
                other.errorCode == errorCode));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, errorCode);

  /// Create a copy of ProfileImageState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProfileImageErrorImplCopyWith<_$ProfileImageErrorImpl> get copyWith =>
      __$$ProfileImageErrorImplCopyWithImpl<_$ProfileImageErrorImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() uploading,
    required TResult Function(String imageUrl) uploaded,
    required TResult Function(String message, String? errorCode) error,
  }) {
    return error(message, errorCode);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? uploading,
    TResult? Function(String imageUrl)? uploaded,
    TResult? Function(String message, String? errorCode)? error,
  }) {
    return error?.call(message, errorCode);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? uploading,
    TResult Function(String imageUrl)? uploaded,
    TResult Function(String message, String? errorCode)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message, errorCode);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ProfileImageInitial value) initial,
    required TResult Function(ProfileImageUploading value) uploading,
    required TResult Function(ProfileImageUploaded value) uploaded,
    required TResult Function(ProfileImageError value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ProfileImageInitial value)? initial,
    TResult? Function(ProfileImageUploading value)? uploading,
    TResult? Function(ProfileImageUploaded value)? uploaded,
    TResult? Function(ProfileImageError value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ProfileImageInitial value)? initial,
    TResult Function(ProfileImageUploading value)? uploading,
    TResult Function(ProfileImageUploaded value)? uploaded,
    TResult Function(ProfileImageError value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class ProfileImageError implements ProfileImageState {
  const factory ProfileImageError(
      {required final String message,
      final String? errorCode}) = _$ProfileImageErrorImpl;

  String get message;
  String? get errorCode;

  /// Create a copy of ProfileImageState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProfileImageErrorImplCopyWith<_$ProfileImageErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
