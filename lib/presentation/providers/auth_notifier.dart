import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/auth/auth_service.dart';
import '../../domain/entities/user.dart';
import '../../core/di/service_locator.dart';
import 'auth_state.dart';

/// StateNotifier for managing authentication state and operations
class AuthNotifier extends StateNotifier<AuthState> {
  final AuthService _authService;

  AuthNotifier(this._authService) : super(const AuthState.initial());

  /// Initialize authentication state by checking stored token
  Future<void> initialize() async {
    try {
      state = const AuthState.loading();

      // Check if user has valid session
      final hasValidSession = await _authService.hasValidSession();
      if (!hasValidSession) {
        state = const AuthState.unauthenticated();
        return;
      }

      // Verify token with server
      final isTokenValid = await _authService.verifyToken();
      if (!isTokenValid) {
        state = const AuthState.unauthenticated();
        return;
      }

      // Get current user data
      final user = await _authService.getCurrentUser();
      if (user != null) {
        state = AuthState.authenticated(user: user);
      } else {
        state = const AuthState.unauthenticated();
      }
    } catch (e) {
      state = AuthState.error(
        message: 'Failed to initialize authentication: ${e.toString()}',
        errorCode: 'INIT_ERROR',
      );
    }
  }

  /// Login with email and password
  Future<void> login(String email, String password) async {
    try {
      state = const AuthState.loading();

      final result = await _authService.login(email, password);

      result.when(
        success: (token, user) {
          state = AuthState.authenticated(user: user);
        },
        failure: (message, errorCode) {
          state = AuthState.error(message: message, errorCode: errorCode);
        },
      );
    } catch (e) {
      state = AuthState.error(
        message: 'Login failed: ${e.toString()}',
        errorCode: 'LOGIN_ERROR',
      );
    }
  }

  /// Register new user
  Future<void> register({
    required String email,
    required String password,
    required String name,
    required UserType userType,
    String? phone,
  }) async {
    try {
      state = const AuthState.loading();

      final result = await _authService.register(
        email: email,
        password: password,
        name: name,
        userType: userType,
        phone: phone,
      );

      result.when(
        success: (token, user) {
          state = AuthState.authenticated(user: user);
        },
        failure: (message, errorCode) {
          state = AuthState.error(message: message, errorCode: errorCode);
        },
      );
    } catch (e) {
      state = AuthState.error(
        message: 'Registration failed: ${e.toString()}',
        errorCode: 'REGISTER_ERROR',
      );
    }
  }

  /// Logout current user
  Future<void> logout() async {
    try {
      state = const AuthState.loading();

      await _authService.logout();
      state = const AuthState.unauthenticated();
    } catch (e) {
      // Even if logout fails, set state to unauthenticated
      // since local data should be cleared
      state = const AuthState.unauthenticated();
    }
  }

  /// Refresh user data
  Future<void> refreshUser() async {
    try {
      // Only refresh if currently authenticated
      if (!state.isAuthenticated) return;

      final user = await _authService.getCurrentUser();
      if (user != null) {
        state = AuthState.authenticated(user: user);
      } else {
        state = const AuthState.unauthenticated();
      }
    } catch (e) {
      state = AuthState.error(
        message: 'Failed to refresh user data: ${e.toString()}',
        errorCode: 'REFRESH_ERROR',
      );
    }
  }

  /// Verify current token
  Future<void> verifyToken() async {
    try {
      final isValid = await _authService.verifyToken();
      if (!isValid && state.isAuthenticated) {
        state = const AuthState.unauthenticated();
      }
    } catch (e) {
      if (state.isAuthenticated) {
        state = const AuthState.unauthenticated();
      }
    }
  }

  /// Clear error state
  void clearError() {
    if (state.hasError) {
      state = const AuthState.unauthenticated();
    }
  }
}

/// Provider for AuthService
final authServiceProvider = Provider<AuthService>((ref) {
  return getIt<AuthService>();
});

/// Provider for AuthNotifier
final authNotifierProvider = StateNotifierProvider<AuthNotifier, AuthState>((
  ref,
) {
  return AuthNotifier(ref.read(authServiceProvider));
});

/// Provider for current authentication state
final authStateProvider = Provider<AuthState>((ref) {
  return ref.watch(authNotifierProvider);
});

/// Provider for current authenticated user
final currentUserProvider = Provider<User?>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.user;
});

/// Provider for authentication status
final isAuthenticatedProvider = Provider<bool>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.isAuthenticated;
});

/// Provider for loading state
final isAuthLoadingProvider = Provider<bool>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.isLoading;
});

/// Provider for error state
final authErrorProvider = Provider<String?>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.errorMessage;
});

/// Provider for token verification
final tokenVerificationProvider = FutureProvider<bool>((ref) async {
  final authService = ref.read(authServiceProvider);
  return await authService.verifyToken();
});
