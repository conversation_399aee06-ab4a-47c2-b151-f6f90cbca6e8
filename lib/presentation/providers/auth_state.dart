import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/user.dart';

part 'auth_state.freezed.dart';

/// Authentication state representing different states of user authentication
@freezed
class AuthState with _$AuthState {
  /// Initial state when app starts
  const factory AuthState.initial() = AuthInitial;

  /// Loading state during authentication operations
  const factory AuthState.loading() = AuthLoading;

  /// Authenticated state with user data
  const factory AuthState.authenticated({required User user}) =
      AuthAuthenticated;

  /// Unauthenticated state when user is not logged in
  const factory AuthState.unauthenticated() = AuthUnauthenticated;

  /// Error state with error message
  const factory AuthState.error({required String message, String? errorCode}) =
      AuthError;
}

/// Extension to provide convenient state checking methods
extension AuthStateExtension on AuthState {
  /// Check if user is authenticated
  bool get isAuthenticated => this is AuthAuthenticated;

  /// Check if authentication is in progress
  bool get isLoading => this is AuthLoading;

  /// Check if there's an error
  bool get hasError => this is AuthError;

  /// Get user data if authenticated, null otherwise
  User? get user =>
      maybeWhen(authenticated: (user) => user, orElse: () => null);

  /// Get error message if in error state, null otherwise
  String? get errorMessage =>
      maybeWhen(error: (message, errorCode) => message, orElse: () => null);

  /// Get error code if in error state, null otherwise
  String? get errorCode =>
      maybeWhen(error: (message, errorCode) => errorCode, orElse: () => null);
}
