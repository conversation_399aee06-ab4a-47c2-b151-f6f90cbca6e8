import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/rider_profile.dart';
import '../../domain/repositories/profile_repository.dart';
import '../../core/errors/app_error.dart';
import 'profile_state.dart';
import 'user_profile_notifier.dart';

/// StateNotifier for managing rider profile operations
class RiderProfileNotifier extends StateNotifier<RiderProfileState> {
  final ProfileRepository _profileRepository;

  RiderProfileNotifier({required ProfileRepository profileRepository})
    : _profileRepository = profileRepository,
      super(const RiderProfileState.initial());

  /// Load rider profile data
  Future<void> loadProfile() async {
    try {
      state = const RiderProfileState.loading();

      final profile = await _profileRepository.getRiderProfile();
      state = RiderProfileState.loaded(profile: profile);
    } catch (e) {
      _handleError(e, 'Failed to load rider profile');
    }
  }

  /// Create new rider profile
  Future<void> createProfile(RiderProfile profile) async {
    try {
      state = const RiderProfileState.loading();

      final createdProfile = await _profileRepository.createRiderProfile(
        profile,
      );
      state = RiderProfileState.loaded(profile: createdProfile);
    } catch (e) {
      _handleError(e, 'Failed to create rider profile');
    }
  }

  /// Update existing rider profile
  Future<void> updateProfile(RiderProfile profile) async {
    try {
      state = const RiderProfileState.loading();

      final updatedProfile = await _profileRepository.updateRiderProfile(
        profile,
      );
      state = RiderProfileState.loaded(profile: updatedProfile);
    } catch (e) {
      _handleError(e, 'Failed to update rider profile');
    }
  }

  /// Update emergency contact information
  Future<void> updateEmergencyContact({
    required String name,
    required String phone,
  }) async {
    final currentProfile = state.profile;
    if (currentProfile == null) {
      state = const RiderProfileState.error(
        message: 'No profile found to update',
        errorCode: 'NO_PROFILE',
      );
      return;
    }

    final updatedProfile = currentProfile.copyWith(
      emergencyContactName: name,
      emergencyContactPhone: phone,
      updatedAt: DateTime.now(),
    );

    await updateProfile(updatedProfile);
  }

  /// Update preferred language
  Future<void> updatePreferredLanguage(String language) async {
    final currentProfile = state.profile;
    if (currentProfile == null) {
      state = const RiderProfileState.error(
        message: 'No profile found to update',
        errorCode: 'NO_PROFILE',
      );
      return;
    }

    final updatedProfile = currentProfile.copyWith(
      preferredLanguage: language,
      updatedAt: DateTime.now(),
    );

    await updateProfile(updatedProfile);
  }

  /// Add frequent destination
  Future<void> addFrequentDestination(String destination) async {
    final currentProfile = state.profile;
    if (currentProfile == null) {
      state = const RiderProfileState.error(
        message: 'No profile found to update',
        errorCode: 'NO_PROFILE',
      );
      return;
    }

    final updatedDestinations = List<String>.from(
      currentProfile.frequentDestinations,
    );
    if (!updatedDestinations.contains(destination)) {
      updatedDestinations.add(destination);

      final updatedProfile = currentProfile.copyWith(
        frequentDestinations: updatedDestinations,
        updatedAt: DateTime.now(),
      );

      await updateProfile(updatedProfile);
    }
  }

  /// Remove frequent destination
  Future<void> removeFrequentDestination(String destination) async {
    final currentProfile = state.profile;
    if (currentProfile == null) {
      state = const RiderProfileState.error(
        message: 'No profile found to update',
        errorCode: 'NO_PROFILE',
      );
      return;
    }

    final updatedDestinations = List<String>.from(
      currentProfile.frequentDestinations,
    );
    updatedDestinations.remove(destination);

    final updatedProfile = currentProfile.copyWith(
      frequentDestinations: updatedDestinations,
      updatedAt: DateTime.now(),
    );

    await updateProfile(updatedProfile);
  }

  /// Complete profile setup by updating completion status
  Future<void> completeProfile() async {
    final currentProfile = state.profile;
    if (currentProfile == null) {
      state = const RiderProfileState.error(
        message: 'No profile found to complete',
        errorCode: 'NO_PROFILE',
      );
      return;
    }

    // Check if profile has all required information
    if (!currentProfile.hasRequiredInfo) {
      final missingFields = currentProfile.missingRequiredFields;
      state = RiderProfileState.error(
        message:
            'Please complete the following fields: ${missingFields.join(', ')}',
        errorCode: 'INCOMPLETE_PROFILE',
      );
      return;
    }

    final updatedProfile = currentProfile.copyWith(
      isProfileComplete: true,
      updatedAt: DateTime.now(),
    );

    await updateProfile(updatedProfile);
  }

  /// Refresh profile data from server
  Future<void> refreshProfile() async {
    // Don't show loading state for refresh to avoid UI flicker
    try {
      final profile = await _profileRepository.getRiderProfile();
      state = RiderProfileState.loaded(profile: profile);
    } catch (e) {
      _handleError(e, 'Failed to refresh rider profile');
    }
  }

  /// Clear error state
  void clearError() {
    if (state.hasError) {
      state = const RiderProfileState.initial();
    }
  }

  /// Handle errors consistently
  void _handleError(dynamic error, String defaultMessage) {
    if (error is AppError) {
      state = RiderProfileState.error(
        message: error.message,
        errorCode: error.runtimeType.toString(),
      );
    } else {
      state = RiderProfileState.error(
        message: '$defaultMessage: ${error.toString()}',
        errorCode: 'UNKNOWN_ERROR',
      );
    }
  }
}

/// Provider for RiderProfileNotifier
final riderProfileNotifierProvider =
    StateNotifierProvider<RiderProfileNotifier, RiderProfileState>((ref) {
      return RiderProfileNotifier(
        profileRepository: ref.read(profileRepositoryProvider),
      );
    });

/// Convenience providers for accessing rider profile state properties
final riderProfileStateProvider = Provider<RiderProfileState>((ref) {
  return ref.watch(riderProfileNotifierProvider);
});

final currentRiderProfileProvider = Provider<RiderProfile?>((ref) {
  final state = ref.watch(riderProfileStateProvider);
  return state.profile;
});

final isRiderProfileLoadingProvider = Provider<bool>((ref) {
  final state = ref.watch(riderProfileStateProvider);
  return state.isLoading;
});

final riderProfileErrorProvider = Provider<String?>((ref) {
  final state = ref.watch(riderProfileStateProvider);
  return state.errorMessage;
});

final riderProfileCompletionProvider = Provider<double>((ref) {
  final profile = ref.watch(currentRiderProfileProvider);
  return profile?.completionPercentage ?? 0.0;
});

final riderProfileMissingFieldsProvider = Provider<List<String>>((ref) {
  final profile = ref.watch(currentRiderProfileProvider);
  return profile?.missingRequiredFields ?? [];
});

final isRiderProfileCompleteProvider = Provider<bool>((ref) {
  final profile = ref.watch(currentRiderProfileProvider);
  return profile?.isProfileComplete ?? false;
});
