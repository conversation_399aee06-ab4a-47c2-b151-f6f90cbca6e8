import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/user.dart';
import '../../domain/repositories/user_repository.dart';
import '../../domain/repositories/profile_repository.dart';
import '../../core/di/service_locator.dart';
import '../../core/errors/app_error.dart';
import 'profile_state.dart';

/// StateNotifier for managing user profile operations
class UserProfileNotifier extends StateNotifier<UserProfileState> {
  final UserRepository _userRepository;

  UserProfileNotifier({required UserRepository userRepository})
    : _userRepository = userRepository,
      super(const UserProfileState.initial());

  /// Load user profile data
  Future<void> loadProfile() async {
    try {
      state = const UserProfileState.loading();

      final user = await _userRepository.getProfile();
      state = UserProfileState.loaded(user: user);
    } catch (e) {
      _handleError(e, 'Failed to load profile');
    }
  }

  /// Update user profile information
  Future<void> updateProfile({String? name, String? phone}) async {
    try {
      // Get current user from state BEFORE setting to loading
      final currentUser = state.user;
      if (currentUser == null) {
        throw Exception('No current user found');
      }

      // Set loading state after getting current user
      state = const UserProfileState.loading();

      // Create updated user with new values
      final updatedUser = currentUser.copyWith(
        name: name ?? currentUser.name,
        phone: phone ?? currentUser.phone,
      );

      final user = await _userRepository.updateProfile(updatedUser);
      state = UserProfileState.loaded(user: user);
    } catch (e) {
      _handleError(e, 'Failed to update profile');
    }
  }

  /// Update user profile information with full User object
  Future<void> updateProfileWithUser(User updatedUser) async {
    try {
      state = const UserProfileState.loading();

      final user = await _userRepository.updateProfile(updatedUser);
      state = UserProfileState.loaded(user: user);
    } catch (e) {
      _handleError(e, 'Failed to update profile');
    }
  }

  /// Refresh profile data from server
  Future<void> refreshProfile() async {
    // Don't show loading state for refresh to avoid UI flicker
    try {
      final user = await _userRepository.getProfileFromServer();
      state = UserProfileState.loaded(user: user);
    } catch (e) {
      _handleError(e, 'Failed to refresh profile');
    }
  }

  /// Clear error state
  void clearError() {
    if (state.hasError) {
      state = const UserProfileState.initial();
    }
  }

  /// Handle errors consistently
  void _handleError(dynamic error, String defaultMessage) {
    if (error is AppError) {
      state = UserProfileState.error(
        message: error.message,
        errorCode: error.runtimeType.toString(),
      );
    } else {
      state = UserProfileState.error(
        message: '$defaultMessage: ${error.toString()}',
        errorCode: 'UNKNOWN_ERROR',
      );
    }
  }
}

/// StateNotifier for managing profile image operations
class ProfileImageNotifier extends StateNotifier<ProfileImageState> {
  final ProfileRepository _profileRepository;

  ProfileImageNotifier({required ProfileRepository profileRepository})
    : _profileRepository = profileRepository,
      super(const ProfileImageState.initial());

  /// Upload profile image
  Future<void> uploadImage(String imagePath) async {
    try {
      state = const ProfileImageState.uploading();

      final imageUrl = await _profileRepository.uploadProfileImage(imagePath);
      state = ProfileImageState.uploaded(imageUrl: imageUrl);
    } catch (e) {
      _handleError(e, 'Failed to upload image');
    }
  }

  /// Delete profile image
  Future<void> deleteImage() async {
    try {
      state = const ProfileImageState.uploading();

      await _profileRepository.deleteProfileImage();
      state = const ProfileImageState.initial();
    } catch (e) {
      _handleError(e, 'Failed to delete image');
    }
  }

  /// Clear state
  void clearState() {
    state = const ProfileImageState.initial();
  }

  /// Handle errors consistently
  void _handleError(dynamic error, String defaultMessage) {
    if (error is AppError) {
      state = ProfileImageState.error(
        message: error.message,
        errorCode: error.runtimeType.toString(),
      );
    } else {
      state = ProfileImageState.error(
        message: '$defaultMessage: ${error.toString()}',
        errorCode: 'UNKNOWN_ERROR',
      );
    }
  }
}

/// Provider for UserRepository
final userRepositoryProvider = Provider<UserRepository>((ref) {
  return getIt<UserRepository>();
});

/// Provider for ProfileRepository
final profileRepositoryProvider = Provider<ProfileRepository>((ref) {
  return getIt<ProfileRepository>();
});

/// Provider for UserProfileNotifier
final userProfileNotifierProvider =
    StateNotifierProvider<UserProfileNotifier, UserProfileState>((ref) {
      return UserProfileNotifier(
        userRepository: ref.read(userRepositoryProvider),
      );
    });

/// Provider for ProfileImageNotifier
final profileImageNotifierProvider =
    StateNotifierProvider<ProfileImageNotifier, ProfileImageState>((ref) {
      return ProfileImageNotifier(
        profileRepository: ref.read(profileRepositoryProvider),
      );
    });

/// Convenience providers for accessing state properties
final userProfileStateProvider = Provider<UserProfileState>((ref) {
  return ref.watch(userProfileNotifierProvider);
});

final profileImageStateProvider = Provider<ProfileImageState>((ref) {
  return ref.watch(profileImageNotifierProvider);
});

final currentUserProfileProvider = Provider<User?>((ref) {
  final state = ref.watch(userProfileStateProvider);
  return state.user;
});

final isUserProfileLoadingProvider = Provider<bool>((ref) {
  final state = ref.watch(userProfileStateProvider);
  return state.isLoading;
});

final userProfileErrorProvider = Provider<String?>((ref) {
  final state = ref.watch(userProfileStateProvider);
  return state.errorMessage;
});

final isProfileImageUploadingProvider = Provider<bool>((ref) {
  final state = ref.watch(profileImageStateProvider);
  return state.isUploading;
});

final profileImageUrlProvider = Provider<String?>((ref) {
  final state = ref.watch(profileImageStateProvider);
  return state.imageUrl;
});

final profileImageErrorProvider = Provider<String?>((ref) {
  final state = ref.watch(profileImageStateProvider);
  return state.errorMessage;
});
