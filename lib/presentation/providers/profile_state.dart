import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/user.dart';
import '../../domain/entities/rider_profile.dart';

part 'profile_state.freezed.dart';

/// State for user profile management
@freezed
class UserProfileState with _$UserProfileState {
  const factory UserProfileState.initial() = UserProfileInitial;
  const factory UserProfileState.loading() = UserProfileLoading;
  const factory UserProfileState.loaded({required User user}) =
      UserProfileLoaded;
  const factory UserProfileState.error({
    required String message,
    String? errorCode,
  }) = UserProfileError;
}

/// State for rider profile management
@freezed
class RiderProfileState with _$RiderProfileState {
  const factory RiderProfileState.initial() = RiderProfileInitial;
  const factory RiderProfileState.loading() = RiderProfileLoading;
  const factory RiderProfileState.loaded({RiderProfile? profile}) =
      RiderProfileLoaded;
  const factory RiderProfileState.error({
    required String message,
    String? errorCode,
  }) = RiderProfileError;
}

/// State for profile image operations
@freezed
class ProfileImageState with _$ProfileImageState {
  const factory ProfileImageState.initial() = ProfileImageInitial;
  const factory ProfileImageState.uploading() = ProfileImageUploading;
  const factory ProfileImageState.uploaded({required String imageUrl}) =
      ProfileImageUploaded;
  const factory ProfileImageState.error({
    required String message,
    String? errorCode,
  }) = ProfileImageError;
}

/// Extensions for state checking
extension UserProfileStateExtension on UserProfileState {
  bool get isLoading => this is UserProfileLoading;
  bool get isLoaded => this is UserProfileLoaded;
  bool get hasError => this is UserProfileError;

  User? get user => maybeWhen(loaded: (user) => user, orElse: () => null);

  String? get errorMessage =>
      maybeWhen(error: (message, errorCode) => message, orElse: () => null);
}

extension RiderProfileStateExtension on RiderProfileState {
  bool get isLoading => this is RiderProfileLoading;
  bool get isLoaded => this is RiderProfileLoaded;
  bool get hasError => this is RiderProfileError;

  RiderProfile? get profile =>
      maybeWhen(loaded: (profile) => profile, orElse: () => null);

  String? get errorMessage =>
      maybeWhen(error: (message, errorCode) => message, orElse: () => null);
}

extension ProfileImageStateExtension on ProfileImageState {
  bool get isUploading => this is ProfileImageUploading;
  bool get isUploaded => this is ProfileImageUploaded;
  bool get hasError => this is ProfileImageError;

  String? get imageUrl =>
      maybeWhen(uploaded: (imageUrl) => imageUrl, orElse: () => null);

  String? get errorMessage =>
      maybeWhen(error: (message, errorCode) => message, orElse: () => null);
}
