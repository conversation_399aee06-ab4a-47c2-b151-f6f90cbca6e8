# Lucian Rides MVP - Product Requirements Document

## 1. Executive Summary

Tourism-focused ride-sharing platform for St. Lucia connecting tourists with local drivers. FastAPI backend with Firebase Authentication, Firestore database, and Stripe payment processing.

**Target Market**: Tourists visiting St. Lucia
**MVP Goal**: Point-to-point fixed location-destination transfers with fixed pricing rates

## 2. Target Users

### Riders
Tourists visiting St. Lucia needing reliable transportation between predefined pickup/dropoff points.

### Drivers
Local St. Lucia residents with valid driver's license and any vehicle type, seeking flexible income.

## 3. Core Features & Requirements

### 3.1 Authentication & User Management
- Firebase Authentication (email/password, Google)
- Phone number verification (optional)
- User profiles (name, photo, contact info)
- User types: riders, drivers, admins

### 3.2 Ride Booking System
- **Ride Type**: Point-to-point transfers only
- Predefined pickup/dropoff locations with fixed pricing
- Real-time driver matching within 5km radius
- Ride confirmation and status tracking
- Trip history and receipts

### 3.3 Fixed Pricing System
Dictionary-based pricing for predefined routes:
- Pickup location → Dropoff location = Fixed USD rate
- No dynamic pricing or distance calculation
- Transparent upfront pricing display

### 3.4 Real-time Tracking
- Real-time driver location updates (Firestore real-time listeners)
- Live ride tracking for passengers
- Driver navigation assistance
- Pickup/dropoff confirmation

### 3.5 Driver-Rider Messaging
- In-app text messaging between driver and rider
- Message history stored in Firestore
- Push notifications for new messages

### 3.6 Payment Processing
**Stripe Integration**:
- Secure payment processing (USD only)
- Card payments and digital wallets
- Automatic fare processing
- Receipt generation

**Stripe Connect for Drivers**:
- Driver onboarding with KYC verification
- Automatic weekly payouts
- 20% platform commission
- Driver earnings tracking

### 3.7 Rating System
- Two-way rating (1-5 stars)
- Riders rate drivers, drivers rate riders
- Rating history and averages
- Optional written feedback

### 3.8 Admin APIs
- View all rides endpoint
- Payment tracking endpoint

## 4. Technical Architecture

### 4.1 Project Structure
```
lucian-backend/ (root directory) (NB: You are currently in this directory)
├── app/
│   ├── __init__.py
│   ├── main.py
│   ├── dependencies.py
│   ├── routers/
│   │   ├── __init__.py
│   │   ├── auth.py
│   │   ├── riders.py
│   │   ├── drivers.py
│   │   ├── rides.py
│   │   ├── payments.py
│   │   └── messages.py
│   ├── internal/
│   │   ├── __init__.py
│   │   └── admin.py
│   ├── core/
│   │   ├── __init__.py
│   │   ├── config.py
│   │   └── security.py
│   ├── middleware/
│   │   ├── __init__.py
│   │   ├── auth.py
│   │   ├── cors.py
│   │   └── rate_limit.py
│   ├── models/
│   │   ├── __init__.py
│   │   ├── user.py
│   │   ├── ride.py
│   │   ├── payment.py
│   │   └── message.py
│   ├── schemas/
│   │   ├── __init__.py
│   │   ├── user.py
│   │   ├── ride.py
│   │   ├── payment.py
│   │   └── message.py
│   ├── services/
│   │   ├── __init__.py
│   │   ├── auth_service.py
│   │   ├── ride_service.py
│   │   ├── payment_service.py
│   │   ├── notification_service.py
│   │   └── messaging_service.py
│   └── db/
│       ├── __init__.py
│       └── firebase.py
├── tests/venv
├── Docs
├── logs
├── .venv
├── .cursor
├── uv.lock
├── .gitignore
├── pyproject.toml
└── README.md
```

### 4.2 Package Management
**UV Package Manager**:
- Use `uv` for dependency management and virtual environments
- `pyproject.toml` for project configuration
- `uv add fastapi firebase-admin stripe` for dependencies

### 4.3 Core API Endpoints

**Authentication**
```
POST /auth/register
POST /auth/login
POST /auth/logout
GET /auth/profile
PUT /auth/profile
```

**Riders**
```
GET /riders/profile
PUT /riders/profile
POST /riders/rides/request
GET /riders/rides/history
POST /riders/rides/{ride_id}/rate
GET /riders/pricing
```

**Drivers**
```
GET /drivers/profile
PUT /drivers/profile
POST /drivers/availability
GET /drivers/rides/active
POST /drivers/rides/{ride_id}/accept
PUT /drivers/rides/{ride_id}/status
POST /drivers/rides/{ride_id}/location
GET /drivers/earnings
```

**Rides**
```
GET /rides/{ride_id}
PUT /rides/{ride_id}/status
GET /rides/active
```

**Messages**
```
GET /messages/{ride_id}
POST /messages/{ride_id}
```

**Payments**
```
POST /payments/process
GET /payments/history
POST /payments/drivers/onboard
GET /payments/drivers/earnings
```

**Admin**
```
GET /admin/rides
GET /admin/payments
```

## 5. Database Schema (Firestore)

### Users Collection
```json
{
  "id": "user_id", // Required
  "email": "<EMAIL>", // Required
  "phone": "+1758555xxxx", // optional
  "name": "John Doe", // Required
  "user_type": "rider|driver|admin", // Required
  "profile_image": "gs://bucket/image.jpg", // Optional
  "created_at": "2025-01-01T00:00:00Z", // Required
  "updated_at": "2025-01-01T00:00:00Z", // Required
  "is_active": true, // Required, default: true
  "language": "en" // Required, default: "en"
}
```

### Riders Subcollection
```json
{
  "user_id": "user_id", // Required
  "rating": 4.5, // Optional, default: 0
  "total_rides": 25, // Required, default: 0
  "payment_methods": ["card_id_1"], // Optional
  "emergency_contact": { // Optional
    "name": "Jane Doe",
    "phone": "+1758555xxxx"
  }
}
```

### Drivers Subcollection
```json
{
  "user_id": "user_id", // Required
  "license_number": "LC12345", // Required
  "vehicle_info": { // Required
    "make": "Toyota",
    "model": "Corolla",
    "year": 2020,
    "color": "White",
    "license_plate": "ABC123"
  },
  "is_verified": false, // Required, default: false
  "is_available": false, // Required, default: false
  "rating": 4.8, // Optional, default: 0
  "total_rides": 156, // Required, default: 0
  "current_location": { // Optional
    "latitude": 14.0101,
    "longitude": -60.9875,
    "updated_at": "2025-01-01T00:00:00Z"
  },
  "stripe_account_id": "acct_xxxxx", // Optional
  "earnings": { // Required
    "total": 0.00,
    "this_week": 0.00,
    "pending": 0.00
  }
}
```

### Rides Collection
```json
{
  "id": "ride_id", // Required
  "rider_id": "user_id", // Required
  "driver_id": "user_id", // Optional (null until accepted)
  "status": "requested|accepted|in_progress|completed|cancelled", // Required
  "pickup_location": { // Required
    "name": "Hewanorra Airport",
    "address": "Hewanorra International Airport",
    "latitude": 13.7325,
    "longitude": -60.9526
  },
  "dropoff_location": { // Required
    "name": "Sandals Grande",
    "address": "Sandals Grande St. Lucian Resort",
    "latitude": 14.0101,
    "longitude": -60.9875
  },
  "fixed_fare": 45.00, // Required
  "ride_type": "point_to_point", // Required
  "payment_status": "pending|completed|failed", // Required
  "payment_intent_id": "pi_xxxxx", // Optional
  "created_at": "2025-01-01T00:00:00Z", // Required
  "accepted_at": "2025-01-01T00:05:00Z", // Optional
  "started_at": "2025-01-01T00:10:00Z", // Optional
  "completed_at": "2025-01-01T00:43:00Z", // Optional
  "ratings": { // Optional
    "rider_rating": 5,
    "driver_rating": 4,
    "rider_feedback": "Great driver!",
    "driver_feedback": "Polite passenger"
  }
}
```

### Messages Collection
```json
{
  "id": "message_id", // Required
  "ride_id": "ride_id", // Required
  "sender_id": "user_id", // Required
  "sender_type": "rider|driver", // Required
  "message": "I'm waiting at the main entrance", // Required
  "created_at": "2025-01-01T00:00:00Z", // Required
  "is_read": false // Required, default: false
}
```

### Locations Collection (Real-time Tracking)
```json
{
  "id": "location_id", // Required
  "ride_id": "ride_id", // Required
  "driver_id": "user_id", // Required
  "coordinates": { // Required
    "latitude": 14.0101,
    "longitude": -60.9875
  },
  "timestamp": "2025-01-01T00:00:00Z", // Required
  "heading": 45, // Optional
  "speed": 25.5 // Optional
}
```

### Payments Collection
```json
{
  "id": "payment_id", // Required
  "ride_id": "ride_id", // Required
  "rider_id": "user_id", // Required
  "driver_id": "user_id", // Required
  "amount": 45.00, // Required
  "currency": "USD", // Required
  "platform_fee": 9.00, // Required (20%)
  "driver_earnings": 36.00, // Required (80%)
  "payment_method": "card_xxxx", // Required
  "stripe_payment_intent": "pi_xxxxx", // Required
  "status": "pending|succeeded|failed", // Required
  "created_at": "2025-01-01T00:00:00Z", // Required
  "processed_at": "2025-01-01T00:01:00Z" // Optional
}
```

### FixedPricing Collection
```json
{
  "id": "pricing_id", // Required
  "pickup_location": "Hewanorra Airport", // Required
  "dropoff_location": "Sandals Grande", // Required
  "price_usd": 45.00, // Required
  "distance_km": 42.5, // Optional
  "estimated_duration": 35, // Optional (minutes)
  "is_active": true // Required, default: true
}
```

## 6. External Integrations

### Firebase Services
- **Firebase Auth**: User authentication
- **Firestore**: Database with real-time listeners
- **Firebase Cloud Messaging**: Push notifications
- **Firebase Storage**: Profile images

### Stripe Integration
- **Stripe Connect**: Driver payouts and KYC
- **Payment Intents API**: Ride payments
- **Webhooks**: Payment status updates

### Google Maps API
- **Maps JavaScript API**: Location display
- **Places API**: Address autocomplete
- **Directions API**: Route calculation

## 7. Development Phases (not strict but you get the idea)

### Phase 1: Core Backend Setup (Week 1)
- FastAPI project structure with UV package manager
- Firebase Authentication integration
- Firestore database connection
- Basic user registration/login endpoints
- Pydantic models and schemas

### Phase 2: User Management (Week 2)
- User profile management (riders/drivers)
- Driver vehicle information and verification
- Profile image upload to Firebase Storage
- User type separation logic

### Phase 3: Ride System (Week 3)
- Fixed pricing system with predefined routes
- Ride request and matching logic
- Driver availability management
- Ride status tracking and updates

### Phase 4: Real-time Features (Week 4)
- Real-time location tracking with Firestore listeners
- Driver-rider messaging system
- Push notifications for ride updates
- Live ride tracking implementation

### Phase 5: Payment Integration (Week 5)
- Stripe payment processing for rides
- Stripe Connect driver onboarding
- Payment status tracking
- Driver earnings calculation and payouts

### Phase 6: Rating & Admin (Week 6)
- Two-way rating system
- Trip history and receipts
- Admin API endpoints for rides and payments
- Basic analytics endpoints

## 8. Technical Requirements

### Performance
- API response time: <500ms
- Real-time updates: <2 seconds
- Database queries: <200ms

### Security
- JWT authentication with Firebase
- Input validation with Pydantic
- HTTPS encryption
- Rate limiting

### Dependencies (UV Management)
- NEVER modify `pyproject.toml` . If you want to add dependencies, use `uv add dep1 dep2`
- To run server or any python file, ALWAYS use `uv run ...`
---

*This PRD provides essential specifications to build the St. Lucia ride-sharing MVP backend with fixed pricing, real-time tracking, messaging, and payment processing.*